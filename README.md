# Storyteller Tactics Chat App

This application provides a chat interface to interact with the o4-mini language model and explore Storyteller Tactics.

## Features

- Backend built with Python (FastAPI).
- Frontend built with HTML, Tailwind CSS, and DaisyUI (lofi theme).
- Interaction with the o4-mini language model via OpenAI API.
- Display of Storyteller Tactics categories and frameworks.
- Chat interface to ask questions and get more information about tactics.

## Setup

1. **Clone the repository (if applicable) or ensure you have the project files.**

2. **Create and activate a virtual environment (recommended):**

   ```bash
   python -m venv venv
   # On Windows
   .\venv\Scripts\activate
   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**

   You need to set the `OPENAI_API_KEY` as a system environment variable. The method for doing this depends on your operating system:

   - **Windows:**

     You can set it temporarily for the current command prompt session:

     ```bash
     set OPENAI_API_KEY=your_openai_api_key_here
     ```

     Or set it permanently through the System Properties:

     1. Search for "environment variables" in the Start Menu.
     2. Click on "Edit the system environment variables".
     3. Click the "Environment Variables..." button.
     4. In the "System variables" section (or "User variables" for your user only), click "New..." (or find `OPENAI_API_KEY` and click "Edit...").
     5. Variable name: `OPENAI_API_KEY`
     6. Variable value: `your_openai_api_key_here`
     7. Click OK on all dialogs. You may need to restart your command prompt or IDE for the changes to take effect.

   - **macOS/Linux:**

     You can set it temporarily for the current terminal session:

     ```bash
     export OPENAI_API_KEY='your_openai_api_key_here'
     ```

     To set it permanently, add the `export` line to your shell's configuration file (e.g., `~/.bashrc`, `~/.zshrc`, `~/.profile`) and then source the file (e.g., `source ~/.bashrc`) or open a new terminal session.

   Replace `your_openai_api_key_here` with your actual OpenAI API key.

## Running the Application

1. **Ensure your virtual environment is activated and you are in the project root directory (`c:\0000_Proj\story0101`).**

2. **Start the FastAPI server:**

   ```bash
   uvicorn main:app --reload
   ```

   The `--reload` flag enables auto-reloading when code changes, which is useful for development.

3. **Open your web browser and navigate to:**

   [http://127.0.0.1:8000](http://127.0.0.1:8000)

## Project Structure

- `main.py`: FastAPI application logic, API endpoints.
- `static/`: Contains frontend files.
  - `index.html`: Main HTML page.
  - `script.js`: JavaScript for frontend interactivity.
  - (Potentially `style.css` for additional custom styles).
- `requirements.txt`: Python dependencies.
- `README.md`: This file.
