import streamlit as st
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import json
import fitz  # PyMuPDF
from datetime import datetime
import os
from google import genai
from google.genai import types
import tempfile
import time
import random

# Configuration Constants
BASE_DIR = Path("storyteller_cards")
PDF_DIR = BASE_DIR / "pdfs"
CARD_SETS_DIR = BASE_DIR / "card_sets"
STORY_PATTERNS_DIR = BASE_DIR / "story_patterns"
PROGRESS_DIR = BASE_DIR / "progress"
MODEL = "gemini-2.0-flash"  # Standard model

# Mc<PERSON><PERSON>ey style colors
MCKINSEY_BLUE = "#003DA5"
MCKINSEY_LIGHT_BLUE = "#D4E6FC"
MCKINSEY_GRAY = "#EAECEF"
MCKINSEY_DARK_GRAY = "#5A6978"

# Apply <PERSON><PERSON><PERSON><PERSON><PERSON> styling
def apply_mckinsey_style():
    st.markdown(
        """
        <style>
        .main {
            background-color: white;
        }
        .stApp {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        h1, h2, h3 {
            color: #003DA5;
            font-weight: 600;
        }
        .stTabs [data-baseweb="tab-list"] {
            gap: 2px;
            background-color: #F7F7F7;
        }
        .stTabs [data-baseweb="tab"] {
            background-color: #F7F7F7;
            color: #003DA5;
            border-radius: 4px 4px 0px 0px;
            padding: 10px 16px;
            font-weight: 500;
        }
        .stTabs [aria-selected="true"] {
            background-color: #D4E6FC !important;
            color: #003DA5 !important;
            border-bottom: 2px solid #003DA5;
        }
        .stButton>button {
            color: white;
            background-color: #003DA5;
            border-radius: 4px;
            border: none;
            padding: 8px 16px;
            font-weight: 500;
        }
        .stButton>button:hover {
            background-color: #00297A;
        }
        .stProgress > div > div > div > div {
            background-color: #003DA5;
        }
        .stExpander {
            border: 1px solid #EAECEF;
            border-radius: 4px;
        }
        .stExpander summary {
            background-color: #F7F7F7;
            border-radius: 4px;
            padding: 10px;
        }
        .css-1qg05rk {
            color: #003DA5;
        }
        .sidebar .sidebar-content {
            background-color: #F7F7F7;
        }
        .stRadio > div {
            flex-direction: row;
            gap: 10px;
        }
        .stRadio label {
            background-color: #F7F7F7;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        </style>
        """,
        unsafe_allow_html=True,
    )

# Rate limiting constants
MAX_RETRIES = 5
INITIAL_RETRY_DELAY = 2  # seconds
MAX_RETRY_DELAY = 60  # seconds

# Storyteller Tactics Categories with detailed descriptions
STORY_CATEGORIES = {
    "Stories that Sell": "Build trust and convince people of your ability to deliver. Trust is essential for selling, more than just price or quality.",
    "Stories that Motivate": "Inspire people to support your ideas and take action. Show your plan and your underlying motivations.",
    "Stories that Convince": "Explain complex information to a non-expert audience and build trust in your judgment. Avoid overwhelming with data; focus on understanding and trust.",
    "Stories that Connect": "Foster empathy and understanding by showing different perspectives. Stories help people see things from another point of view.",
    "Stories that Explain": "Make abstract strategies understandable and relevant. Bring your strategic direction to life.",
    "Stories that Lead": "Build a stronger and more cohesive team. Share stories of struggles, triumphs, and learnings within the team.",
    "Stories that Impress": "Present ideas confidently and clearly. Avoid jargon and use storytelling techniques to engage your audience."
}

# Storyteller Tactics Frameworks with descriptions
STORY_FRAMEWORKS = {
    # Stories that Sell
    "Simple Sales Stories": "Share relatable success stories of helping others. Use stories about existing customers to convince new ones.",
    "Social Proof": "Use trends, prototypes, and testimonials to strengthen your case.",
    "Rags to Riches": "Tell optimistic stories with the customer as the central figure. A story of rising to success from humble beginnings.",
    "Pitch Perfect": "Condense your message into a concise elevator pitch. Craft compelling pitches using Problem, Solution, Trust formula.",
    "Audience Profile": "Understand your audience and their problems to create targeted sales stories.",
    
    # Stories that Motivate
    "Dragon & the City": "Explain your overarching goals and vision. Frame projects as overcoming a threat to improve the status quo.",
    "Drive Stories": "Articulate your motivations and connect them with your audience's potential motivations.",
    "Three Great Conflicts": "Identify and address the obstacles you need to overcome through fundamental human struggles.",
    "Innovation Curve": "Reassure your audience about the risks involved in your idea based on their risk tolerance.",
    "No Easy Way": "Provide a realistic outlook on the challenges ahead. Acknowledges setbacks on the path to success.",
    
    # Stories that Convince
    "Three is the Magic Number": "Prioritize key facts your audience can remember using patterns of three.",
    "That's Funny": "Share the excitement and insights behind your discoveries. Find unexpected moments that reveal insights.",
    "Data Detectives": "Present data in a narrative format to improve understanding and recall.",
    "Trust Me, I'm an Expert": "Demonstrate your credibility and expertise by showing your values through stories of your actions.",
    "Hero & Guide": "Position yourself as the expert guide assisting your audience. Your user is the hero; you are the guide.",
    
    # Stories that Connect
    "Story Listening": "Understand others by listening to their experiences and stories.",
    "Abstractions": "Observe behavior in addition to asking questions to understand deeper knowledge.",
    "Universal Stories": "Find common ground and shared experiences that resonate across cultures.",
    "Story-ish Conversations": "Look for stories in everyday interactions to uncover insights about people and change.",
    "Circle of Life": "Develop relatable stories based on universal characters and life stages.",
    
    # Stories that Explain
    "Order & Chaos": "Show where your strategy fits in a changing world. Balance between the known and unknown.",
    "Good & Evil": "Define the important battles your strategy addresses through moral conflicts and choices.",
    "What's it About?": "Explain the relevance of the strategy to your colleagues by focusing on change and benefit.",
    "Rolls Royce Moment": "Illustrate your strategy in action with a vivid, exemplary detail that represents the whole story.",
    "Story Hooks": "Make your strategy sound engaging with compelling openings that capture attention.",
    
    # Stories that Lead
    "Curious Tales": "Discover what motivates your team members by exploring what grabs attention.",
    "Man in a Hole": "Frame teamwork as an epic journey with challenges, crisis, and recovery with newfound wisdom.",
    "Emotional Dashboard": "Find stories in the highs and lows of projects by identifying strong emotions.",
    "Thoughtful Failures": "Extract lessons from mistakes by analyzing goals, assumptions, insights, skills, and communication.",
    "Story Bank": "Collect and share valuable team stories for future reference and learning.",
    
    # Stories that Impress
    "Movie Time": "Tell a story, not just present facts. Use vivid descriptions to create a mental movie for your audience.",
    "Five Ts": "Structure your story effectively using Timeline, Turning Points, Tensions, Temptations, Teachable Moments.",
    "Show and Tell": "Make visuals and narration work together effectively to maintain audience attention.",
    "Cut to the Chase": "Have a backup plan if your presentation falters. Regain audience attention by focusing on Action, Emotion, or Meaning.",
    "Secrets & Puzzles": "Engage the audience by hinting at undiscovered information or anomalies to create intrigue."
}

# Framework to Category mapping
FRAMEWORK_CATEGORY_MAP = {
    "Simple Sales Stories": "Stories that Sell",
    "Social Proof": "Stories that Sell",
    "Rags to Riches": "Stories that Sell",
    "Pitch Perfect": "Stories that Sell",
    "Audience Profile": "Stories that Sell",
    
    "Dragon & the City": "Stories that Motivate",
    "Drive Stories": "Stories that Motivate",
    "Three Great Conflicts": "Stories that Motivate",
    "Innovation Curve": "Stories that Motivate",
    "No Easy Way": "Stories that Motivate",
    
    "Three is the Magic Number": "Stories that Convince",
    "That's Funny": "Stories that Convince",
    "Data Detectives": "Stories that Convince",
    "Trust Me, I'm an Expert": "Stories that Convince",
    "Hero & Guide": "Stories that Convince",
    
    "Story Listening": "Stories that Connect",
    "Abstractions": "Stories that Connect",
    "Universal Stories": "Stories that Connect",
    "Story-ish Conversations": "Stories that Connect",
    "Circle of Life": "Stories that Connect",
    
    "Order & Chaos": "Stories that Explain",
    "Good & Evil": "Stories that Explain",
    "What's it About?": "Stories that Explain",
    "Rolls Royce Moment": "Stories that Explain",
    "Story Hooks": "Stories that Explain",
    
    "Curious Tales": "Stories that Lead",
    "Man in a Hole": "Stories that Lead",
    "Emotional Dashboard": "Stories that Lead",
    "Thoughtful Failures": "Stories that Lead",
    "Story Bank": "Stories that Lead",
    
    "Movie Time": "Stories that Impress",
    "Five Ts": "Stories that Impress",
    "Show and Tell": "Stories that Impress",
    "Cut to the Chase": "Stories that Impress",
    "Secrets & Puzzles": "Stories that Impress"
}

# Enhanced story elements for more sophisticated analysis
STORY_ELEMENTS = {
    "Characters": "People or entities that drive the narrative or are affected by events",
    "Setting": "The context, environment, or backdrop where events take place",
    "Conflict": "The central problem, challenge, or tension that drives the story",
    "Resolution": "How problems are solved or situations are concluded",
    "Emotion": "The feelings evoked or expressed in the narrative",
    "Surprise": "Unexpected turns, revelations, or insights that captivate attention",
    "Metaphor": "Symbolic comparisons that illustrate complex ideas through familiar concepts",
    "Data Point": "Compelling statistics, numbers, or facts that strengthen the narrative",
    "Quotable Moment": "Memorable phrases, sayings, or statements worth repeating",
    "Transformation": "Changes in perspective, situation, or understanding over time"
}

class StoryCard(BaseModel):
    title: str
    framework: str
    category: str
    emotional_core: str
    content: str
    key_insight: str
    contextual_relevance: str
    storyteller_script: str  # Will follow Hook:Narrative:Powerful Conclusion:Delivery: format
    script_hook: str = ""
    script_narrative: str = ""
    script_conclusion: str = ""
    script_delivery: str = ""
    key_phrases: List[str]
    audience_impact: str
    interest_score: int  # 1-10 rating of how interesting this point is
    audience: List[str]  # Appropriate audiences (general, specialists, children, etc.)
    source: str
    page_reference: List[int]  # Which pages this story was extracted from
    entities: List[str] = []  # Named entities for better searchability
    sentiment: str = ""  # Overall emotional tone
    keywords: List[str] = []  # Key themes and topics


class StoryChunk(BaseModel):
    pages: List[int]
    text: str
    has_story_potential: bool = False
    story_frameworks: List[str] = []
    entities: List[str] = []
    sentiment: str = ""
    keywords: List[str] = []


def setup_directories():
    # Create all necessary directories
    for directory in [PDF_DIR, CARD_SETS_DIR, STORY_PATTERNS_DIR, PROGRESS_DIR]:
        directory.mkdir(parents=True, exist_ok=True)


def save_card_set(story_cards: List[StoryCard], pdf_name: str):
    output_path = CARD_SETS_DIR / f"{pdf_name.replace('.pdf', '')}_cards.json"
    st.toast(f"Saving story card set ({len(story_cards)} cards)...")
    card_dicts = []
    for card in story_cards:
        if isinstance(card, dict):
            card_dicts.append(card)
        else:
            card_dicts.append(card.dict())
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump({"story_cards": card_dicts}, f, indent=2)
    return output_path


def save_progress(pdf_name: str, last_processed_chunk: int):
    progress_path = PROGRESS_DIR / f"{pdf_name.replace('.pdf', '')}_progress.json"
    with open(progress_path, 'w', encoding='utf-8') as f:
        json.dump({"last_processed_chunk": last_processed_chunk}, f, indent=2)
    st.toast(f"Progress saved at chunk {last_processed_chunk}")


def load_progress(pdf_name: str) -> int:
    progress_path = PROGRESS_DIR / f"{pdf_name.replace('.pdf', '')}_progress.json"
    if progress_path.exists():
        with open(progress_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get("last_processed_chunk", 0)
    return 0


def load_existing_card_set(pdf_name: str) -> List[StoryCard]:
    cards_file = CARD_SETS_DIR / f"{pdf_name.replace('.pdf', '')}_cards.json"
    if cards_file.exists():
        st.info("Loading existing story card set...")
        with open(cards_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            st.success(f"Loaded {len(data['story_cards'])} existing story cards")
            return data['story_cards']
    st.info("Creating new story card set")
    return []


def process_with_retry(client, prompt, model, config, stream=False, max_retries=MAX_RETRIES):
    """Process API request with exponential backoff retry logic"""
    for attempt in range(max_retries):
        try:
            if stream:
                return client.models.generate_content_stream(
                    model=model,
                    contents=prompt,
                    config=config,
                )
            else:
                response = client.models.generate_content(
                    model=model,
                    contents=prompt,
                    config=config,
                )
                return response
        except Exception as e:
            error_str = str(e)
            
            # Check if it's a rate limit error
            if "429" in error_str and "RESOURCE_EXHAUSTED" in error_str:
                if attempt < max_retries - 1:  # Don't sleep on the last attempt
                    # Calculate delay with exponential backoff and jitter
                    delay = min(INITIAL_RETRY_DELAY * (2 ** attempt) + random.uniform(0, 1), MAX_RETRY_DELAY)
                    st.warning(f"Rate limit hit. Retrying in {delay:.1f} seconds (attempt {attempt+1}/{max_retries})...")
                    time.sleep(delay)
                else:
                    st.error(f"Maximum retries reached. Error: {e}")
                    raise
            else:
                # Not a rate limit error, just raise it
                st.error(f"API Error: {e}")
                raise
    
    # If we've exhausted all retries
    raise Exception("Maximum retries exceeded")


def prepare_text_chunks(pdf_document, chunk_size: int) -> List[StoryChunk]:
    """Divide the PDF into multi-page chunks for story extraction"""
    chunks = []
    total_pages = pdf_document.page_count
    
    for start_page in range(0, total_pages, chunk_size):
        end_page = min(start_page + chunk_size, total_pages)
        
        # Extract text from this chunk of pages
        chunk_text = ""
        page_numbers = []
        for page_num in range(start_page, end_page):
            page = pdf_document[page_num]
            page_text = page.get_text()
            chunk_text += f"\n\n--- PAGE {page_num + 1} ---\n\n{page_text}"
            page_numbers.append(page_num + 1)
        
        # Create a chunk object
        chunk = StoryChunk(
            pages=page_numbers,
            text=chunk_text
        )
        chunks.append(chunk)
    
    return chunks


def analyze_story_potential(client, chunk: StoryChunk, frameworks: List[str], story_elements: List[str]) -> StoryChunk:
    """
    Enhanced analysis of text chunk to determine story potential with NLP insights
    Provides sentiment analysis, named entity recognition, and keyword extraction
    """
    
    # Prepare framework descriptions for a more informed analysis
    framework_descriptions = []
    for fw in frameworks:
        if fw in STORY_FRAMEWORKS:
            framework_descriptions.append(f"{fw}: {STORY_FRAMEWORKS[fw]}")
    
    # Prepare story elements descriptions
    element_descriptions = []
    for element in story_elements:
        if element in STORY_ELEMENTS:
            element_descriptions.append(f"{element}: {STORY_ELEMENTS[element]}")

    system_prompt = f"""Analyze this chunk of text from a book to determine if it contains potential material for storytelling.

Perform an in-depth analysis including:
1. Sentiment analysis - identify the overall emotional tone (positive, negative, neutral, mixed)
2. Named entity recognition - identify key people, organizations, locations, events
3. Keyword extraction - identify the most important themes and topics
4. Story potential assessment - evaluate if the text contains compelling narrative elements

Focus on identifying if this text contains one or more of these story elements:
{'; '.join(element_descriptions)}

Specifically, evaluate if this text could be used to create story cards fitting any of these storytelling frameworks:
{'; '.join(framework_descriptions[:10])}
{'; '.join(framework_descriptions[10:20])}
{'; '.join(framework_descriptions[20:])}

Your response should be in JSON format with the following structure:
{{
  "has_story_potential": true/false,
  "story_frameworks": ["Framework 1", "Framework 2", ...],
  "sentiment": "Overall emotional tone (positive, negative, neutral, mixed)",
  "entities": ["Entity 1", "Entity 2", ...],  
  "keywords": ["Theme 1", "Theme 2", ...],
  "reasoning": "Detailed explanation of why this chunk has story potential and which frameworks it might fit"
}}"""
    
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=f"{system_prompt}\n\nText chunk to analyze: {chunk.text[:10000]}"),
            ],
        ),
    ]
    
    generate_content_config = types.GenerateContentConfig(
        temperature=0.2,
        top_p=0.95,
        top_k=40,
        max_output_tokens=2048,
        response_mime_type="application/json",
    )
    
    try:
        response = process_with_retry(
            client=client,
            prompt=contents,
            model=MODEL,  # Using the standard model that supports JSON mode
            config=generate_content_config
        )
        
        # Parse the JSON response
        response_json = json.loads(response.text)
        
        # Update the chunk with analysis results
        chunk.has_story_potential = response_json.get("has_story_potential", False)
        chunk.story_frameworks = response_json.get("story_frameworks", [])
        chunk.sentiment = response_json.get("sentiment", "")
        chunk.entities = response_json.get("entities", [])
        chunk.keywords = response_json.get("keywords", [])
        
        return chunk
        
    except Exception as e:
        st.error(f"Error analyzing story potential: {e}")
        return chunk


def extract_story_cards(client, chunk: StoryChunk, frameworks: List[str]) -> List[StoryCard]:
    """
    Enhanced extraction of story cards with improved script structure and NLP insights
    """
    
    if not chunk.has_story_potential or not chunk.story_frameworks:
        return []
    
    # Create framework descriptions for each identified framework
    framework_descriptions = []
    for fw in chunk.story_frameworks:
        if fw in STORY_FRAMEWORKS:
            framework_descriptions.append(f"{fw}: {STORY_FRAMEWORKS[fw]}")
    
    # Updated system prompt with enhanced structure and NLP requirements
    system_prompt = f"""Create compelling story cards based on the text chunk provided.

For each potential story in this text, create a card that follows the Storyteller Tactics framework. Focus on these frameworks that were identified in this text:
{'; '.join(framework_descriptions)}

For each story card, provide:
1. A compelling title that captures the essence of the story
2. The storytelling framework it fits (from the list above)
3. The category it belongs to (based on the framework)
4. The emotional core - identify the primary emotion this story evokes (e.g., inspiration, surprise, concern)
5. Detailed content - provide a rich and comprehensive summary of the story with vivid details
6. Key insight - extract the central lesson or revelation from this story
7. Contextual relevance - explain when and where this story is most effectively used
8. A storyteller script - provide a structured script using this EXACT format:
   
   Hook: [Attention-grabbing opening line or question that immediately engages the audience]
   
   Narrative: [Main body of the story with key details, characters, situations, and progression]
   
   Powerful Conclusion: [Impactful closing that delivers the main point or lesson]
   
   Delivery: [Specific guidance on pacing, pauses, tone shifts, emphasis, gestures, and audience interaction]

9. Key phrases - list 3-5 memorable phrases or powerful quotes from the story
10. Audience impact - describe how this story will affect listeners emotionally and intellectually
11. Appropriate audiences (general, specialists, children, adults, etc.)
12. An interest score (1-10) indicating how engaging this would be in conversation
13. Source - identify the original author and title if available
14. Entities - important people, organizations, or places in the story
15. Keywords - 3-7 key themes or topics in the story
16. Sentiment - the overall emotional tone of the story

Your response should be in JSON format with the following structure:
{{
  "story_cards": [
    {{
      "title": "Compelling title for the story",
      "framework": "One of the frameworks from the list",
      "category": "The corresponding category for this framework",
      "emotional_core": "The primary emotion this story evokes",
      "content": "Detailed and rich description of the story with vivid details",
      "key_insight": "The central lesson or revelation from this story",
      "contextual_relevance": "When and where this story is most effectively used",
      "storyteller_script": "Complete script with all 4 sections",
      "script_hook": "Just the hook section",
      "script_narrative": "Just the narrative section",
      "script_conclusion": "Just the conclusion section",
      "script_delivery": "Just the delivery guidance section",
      "key_phrases": ["memorable phrase 1", "memorable phrase 2", "powerful quote"],
      "audience_impact": "How this story will affect the audience emotionally and intellectually",
      "audience": ["audience1", "audience2", ...],
      "interest_score": number between 1-10,
      "source": "Author_Title",
      "page_reference": [page_numbers],
      "entities": ["Entity1", "Entity2", ...],
      "keywords": ["Theme1", "Theme2", ...],
      "sentiment": "Emotional tone (positive, negative, neutral, mixed)"
    }},
    ...
  ]
}}

Remember that the most powerful story cards are memorable, emotionally resonant, and ready to use in conversations. Focus on capturing the transformative essence of each story."""
    
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text=f"{system_prompt}\n\nText chunk (from pages {chunk.pages[0]}-{chunk.pages[-1]}):\n{chunk.text[:15000]}"),
            ],
        ),
    ]
    
    # Include chunk analysis results to enhance extraction
    additional_context = f"""
Based on preliminary analysis, this text:
- Has these named entities: {', '.join(chunk.entities)}
- Contains these themes: {', '.join(chunk.keywords)}
- Has an overall {chunk.sentiment} sentiment tone

Please use this information to create richer, more targeted story cards.
"""
    
    # Add additional context if we have analysis results
    if chunk.entities or chunk.keywords or chunk.sentiment:
        contents[0].parts[0] = types.Part.from_text(text=f"{system_prompt}\n\n{additional_context}\n\nText chunk (from pages {chunk.pages[0]}-{chunk.pages[-1]}):\n{chunk.text[:15000]}")
    
    generate_content_config = types.GenerateContentConfig(
        temperature=0.7,
        top_p=0.95,
        top_k=40,
        max_output_tokens=8192,
        response_mime_type="application/json",
    )
    
    try:
        response = process_with_retry(
            client=client,
            prompt=contents,
            model=MODEL,
            config=generate_content_config
        )
        
        # Parse the JSON response
        response_json = json.loads(response.text)
        story_cards = response_json.get("story_cards", [])
        
        # For each card, ensure all fields are present and correctly formatted
        for card in story_cards:
            # Ensure the category is correctly mapped from the framework
            framework = card.get("framework", "")
            if framework in FRAMEWORK_CATEGORY_MAP:
                card["category"] = FRAMEWORK_CATEGORY_MAP[framework]
            
            # Split the storyteller script into sections if not already done
            if "storyteller_script" in card and not card.get("script_hook"):
                full_script = card["storyteller_script"]
                
                # Extract individual script components if they don't already exist
                if "Hook:" in full_script and "Narrative:" in full_script:
                    try:
                        # Extract hook
                        hook_start = full_script.find("Hook:") + 5
                        hook_end = full_script.find("Narrative:")
                        card["script_hook"] = full_script[hook_start:hook_end].strip()
                        
                        # Extract narrative
                        narrative_start = full_script.find("Narrative:") + 10
                        narrative_end = full_script.find("Powerful Conclusion:")
                        if narrative_end == -1:  # Try alternative label
                            narrative_end = full_script.find("Conclusion:")
                        card["script_narrative"] = full_script[narrative_start:narrative_end].strip()
                        
                        # Extract conclusion
                        conclusion_start = max(full_script.find("Powerful Conclusion:") + 20, 
                                              full_script.find("Conclusion:") + 11)
                        conclusion_end = full_script.find("Delivery:")
                        card["script_conclusion"] = full_script[conclusion_start:conclusion_end].strip()
                        
                        # Extract delivery
                        delivery_start = full_script.find("Delivery:") + 9
                        card["script_delivery"] = full_script[delivery_start:].strip()
                    except:
                        # If parsing fails, create placeholder sections
                        card["script_hook"] = "Compelling opening line to grab attention."
                        card["script_narrative"] = "Detailed story with key points and progression."
                        card["script_conclusion"] = "Impactful closing that delivers the main point."
                        card["script_delivery"] = "Speak slowly, pause after key points, use hand gestures for emphasis."
                
                # Ensure the script follows the required format 
                if not all(section in card["storyteller_script"] for section in ["Hook:", "Narrative:", "Conclusion:", "Delivery:"]):
                    card["storyteller_script"] = f"""Hook: {card.get("script_hook", "Attention-grabbing opening")}
                    
Narrative: {card.get("script_narrative", "Main body of the story with key details and progression")}

Powerful Conclusion: {card.get("script_conclusion", "Impactful closing that delivers the main point")}

Delivery: {card.get("script_delivery", "Guidance on pacing, pauses, emphasis, and audience interaction")}"""
            
            # Ensure all required fields are present
            for field, default_value in [
                ("emotional_core", "Inspiration"),
                ("key_insight", "Key lesson from the story"),
                ("contextual_relevance", "Best used in professional settings"),
                ("key_phrases", ["Key phrase 1", "Key phrase 2", "Key phrase 3"]),
                ("audience_impact", "Helps listeners understand the importance of the topic"),
                ("source", "Unknown"),
                ("entities", []),
                ("keywords", []),
                ("sentiment", "mixed"),
            ]:
                if field not in card:
                    card[field] = default_value
        
        return story_cards
        
    except Exception as e:
        st.error(f"Error extracting story cards: {e}")
        return []


def process_pdf_chunks(client, pdf_path, pdf_name, chunk_size, start_page=1, end_page=None, story_elements=None):
    """Process PDF in chunks for multi-page story extraction with enhanced analysis"""
    
    # Load existing cards if available
    story_cards = load_existing_card_set(pdf_name)
    
    # Create a PDF document object
    pdf_document = fitz.open(pdf_path)
    total_pages = pdf_document.page_count
    
    # Adjust end_page if necessary
    if end_page is None or end_page > total_pages:
        end_page = total_pages
    
    # Adjust start_page (it's 1-indexed in UI but 0-indexed in code)
    actual_start_page = max(0, start_page - 1)
    actual_end_page = min(end_page, total_pages)
    
    # Create a subset document if processing a range
    if actual_start_page > 0 or actual_end_page < total_pages:
        st.info(f"Processing pages {start_page} to {end_page} of {total_pages} total pages")
        # Adjust for PyMuPDF indexing
        subset_range = range(actual_start_page, actual_end_page)
        total_pages = len(subset_range)
    else:
        st.info(f"Processing all {total_pages} pages")
        subset_range = range(total_pages)
        
    # Divide the PDF into chunks
    chunks = []
    for i in range(0, len(subset_range), chunk_size):
        chunk_pages = []
        chunk_text = ""
        
        # Get actual page numbers for this chunk
        for j in range(i, min(i + chunk_size, len(subset_range))):
            page_idx = subset_range[j]
            page = pdf_document[page_idx]
            page_text = page.get_text()
            chunk_text += f"\n\n--- PAGE {page_idx + 1} ---\n\n{page_text}"
            chunk_pages.append(page_idx + 1)
        
        # Create a chunk object
        chunk = StoryChunk(
            pages=chunk_pages,
            text=chunk_text
        )
        chunks.append(chunk)
    
    total_chunks = len(chunks)
    st.info(f"Created {total_chunks} chunks of approximately {chunk_size} pages each")
    
    # Load previous progress if exists
    start_chunk = 0
    if st.session_state.resume_processing:
        start_chunk = load_progress(pdf_name)
        if start_chunk > 0:
            st.info(f"Resuming from chunk {start_chunk + 1}")
    
    # Set up progress tracking
    progress_bar = st.progress(start_chunk / total_chunks if total_chunks > 0 else 0)
    progress_text = st.empty()
    
    # Create pause button in session state if not exists
    if "pause_processing" not in st.session_state:
        st.session_state.pause_processing = False
    
    # Add a pause button that the user can click during processing
    col1, col2 = st.columns(2)
    with col1:
        pause_button = st.button("⏸️ Pause Processing", use_container_width=True)
    with col2:
        save_button = st.button("💾 Save Progress", use_container_width=True)
    
    # If story_elements is None, use all
    if story_elements is None:
        story_elements = list(STORY_ELEMENTS.keys())
    
    # Process each chunk
    for chunk_idx in range(start_chunk, total_chunks):
        # Check if pause button was clicked
        if pause_button or st.session_state.pause_processing:
            st.session_state.pause_processing = True
            st.warning("Processing paused. Click 'Resume' to continue.")
            save_progress(pdf_name, chunk_idx)
            save_card_set(story_cards, pdf_name)
            break
        
        # Check if save button was clicked
        if save_button:
            save_progress(pdf_name, chunk_idx)
            save_card_set(story_cards, pdf_name)
            st.toast("Progress saved!")
        
        # Update progress display
        progress_text.write(f"Processing chunk {chunk_idx + 1}/{total_chunks} (pages {chunks[chunk_idx].pages[0]}-{chunks[chunk_idx].pages[-1]})")
        
        # Step 1: Analyze if the chunk has story potential with enhanced NLP
        progress_text.write("Analyzing story potential with sentiment analysis, entity recognition, and keyword extraction...")
        analyzed_chunk = analyze_story_potential(
            client=client, 
            chunk=chunks[chunk_idx], 
            frameworks=list(STORY_FRAMEWORKS.keys()),
            story_elements=story_elements
        )
        
        # Step 2: If it has potential, extract story cards
        if analyzed_chunk.has_story_potential:
            # Format insights from analysis for display
            frameworks_str = ', '.join(analyzed_chunk.story_frameworks)
            entities_str = ', '.join(analyzed_chunk.entities[:5]) + ("..." if len(analyzed_chunk.entities) > 5 else "")
            keywords_str = ', '.join(analyzed_chunk.keywords[:5]) + ("..." if len(analyzed_chunk.keywords) > 5 else "")
            
            progress_text.write(f"Found story potential! Sentiment: {analyzed_chunk.sentiment}")
            progress_text.write(f"Identified frameworks: {frameworks_str}")
            progress_text.write(f"Key themes: {keywords_str}")
            progress_text.write("Extracting story cards with enhanced structure...")
            
            new_cards = extract_story_cards(client, analyzed_chunk, list(STORY_FRAMEWORKS.keys()))
            
            if new_cards:
                progress_text.write(f"✅ Extracted {len(new_cards)} story cards from this chunk")
                story_cards.extend(new_cards)
            else:
                progress_text.write("No story cards were extracted from this chunk")
        else:
            progress_text.write("No significant story potential in this chunk, skipping extraction")
        
        # Save progress periodically
        if chunk_idx % 3 == 0:
            save_progress(pdf_name, chunk_idx)
            save_card_set(story_cards, pdf_name)
        
        # Update progress bar
        progress_bar.progress((chunk_idx + 1) / total_chunks)
    
    # Final save
    if not st.session_state.pause_processing:
        save_card_set(story_cards, pdf_name)
        st.success("Processing complete!")
    else:
        st.info("Processing paused. You can resume later.")
        # Add a resume button
        if st.button("▶️ Resume Processing", use_container_width=True):
            st.session_state.pause_processing = False
            st.experimental_rerun()
    
    return story_cards


def generate_markdown_cards(story_cards: List[StoryCard], pdf_name: str):
    """Generate markdown deck of story cards organized by category"""
    if not story_cards:
        st.warning("No story cards to generate markdown")
        return None
    
    # Group by category
    categories = {}
    for card in story_cards:
        category = card.get("category", "Uncategorized")
        if category not in categories:
            categories[category] = []
        categories[category].append(card)
    
    # Sort each category by interest score
    for category in categories:
        categories[category] = sorted(categories[category], key=lambda x: x.get("interest_score", 0), reverse=True)
    
    # Create markdown file
    cards_path = CARD_SETS_DIR / f"{pdf_name.replace('.pdf', '')}_storyteller_cards.md"
    
    with open(cards_path, 'w', encoding='utf-8') as f:
        f.write(f"# Storyteller Tactics Cards: {pdf_name}\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Table of contents
        f.write("## Table of Contents\n\n")
        for category in categories:
            f.write(f"- [{category}](#{category.lower().replace(' ', '-')})\n")
        f.write("\n---\n\n")
        
        for category, cards in categories.items():
            f.write(f"## {category}\n\n")
            if category in STORY_CATEGORIES:
                f.write(f"_{STORY_CATEGORIES[category]}_\n\n")
            
            for i, card in enumerate(cards):
                # Get script sections
                script_hook = card.get('script_hook', '')
                script_narrative = card.get('script_narrative', '')
                script_conclusion = card.get('script_conclusion', '')
                script_delivery = card.get('script_delivery', '')
                
                # Format storyteller script in sections if needed
                storyteller_script = card.get('storyteller_script', '')
                if not (script_hook and script_narrative and script_conclusion and script_delivery):
                    # Try to parse sections from the full script
                    if "Hook:" in storyteller_script and "Narrative:" in storyteller_script:
                        try:
                            # Extract hook
                            hook_start = storyteller_script.find("Hook:") + 5
                            hook_end = storyteller_script.find("Narrative:")
                            script_hook = storyteller_script[hook_start:hook_end].strip()
                            
                            # Extract narrative
                            narrative_start = storyteller_script.find("Narrative:") + 10
                            narrative_end = storyteller_script.find("Powerful Conclusion:")
                            if narrative_end == -1:  # Try alternative label
                                narrative_end = storyteller_script.find("Conclusion:")
                            script_narrative = storyteller_script[narrative_start:narrative_end].strip()
                            
                            # Extract conclusion
                            conclusion_start = max(storyteller_script.find("Powerful Conclusion:") + 20, 
                                                  storyteller_script.find("Conclusion:") + 11)
                            conclusion_end = storyteller_script.find("Delivery:")
                            script_conclusion = storyteller_script[conclusion_start:conclusion_end].strip()
                            
                            # Extract delivery
                            delivery_start = storyteller_script.find("Delivery:") + 9
                            script_delivery = storyteller_script[delivery_start:].strip()
                        except:
                            # If parsing fails, use the whole script
                            pass
                
                # Write card details
                f.write(f"### {card.get('title', f'Card {i+1}')}\n\n")
                f.write(f"**Framework:** {card.get('framework', 'N/A')}  \n")
                f.write(f"**Emotional Core:** {card.get('emotional_core', 'N/A')}  \n")
                
                # Add keywords and entities if available
                if card.get('keywords', []):
                    f.write(f"**Keywords:** {', '.join(card.get('keywords', []))}  \n")
                if card.get('sentiment', ''):
                    f.write(f"**Sentiment:** {card.get('sentiment', '')}  \n")
                
                f.write("\n**Content:**  \n")
                f.write(f"{card.get('content', '')}  \n\n")
                f.write(f"**Key Insight:**  \n{card.get('key_insight', '')}  \n\n")
                f.write(f"**Contextual Relevance:**  \n{card.get('contextual_relevance', '')}  \n\n")
                
                f.write("**Key Phrases:**  \n")
                for phrase in card.get('key_phrases', []):
                    f.write(f"- \"{phrase}\"  \n")
                f.write("\n")
                
                f.write(f"**Audience Impact:**  \n{card.get('audience_impact', '')}  \n\n")
                
                # Write storyteller script in structured format
                f.write("**Storyteller Script:**  \n\n")
                f.write("```\n")
                if script_hook or script_narrative or script_conclusion or script_delivery:
                    # Use parsed sections if available
                    f.write(f"Hook: \n{script_hook}\n\n")
                    f.write(f"Narrative: \n{script_narrative}\n\n")
                    f.write(f"Powerful Conclusion: \n{script_conclusion}\n\n")
                    f.write(f"Delivery: \n{script_delivery}\n")
                else:
                    # Otherwise use the whole script
                    f.write(f"{storyteller_script}\n")
                f.write("```\n\n")
                
                f.write(f"**Interest Score:** {card.get('interest_score', 'N/A')}/10  \n")
                f.write(f"**Suitable for:** {', '.join(card.get('audience', ['General audience']))}  \n")
                f.write(f"**Source:** {card.get('source', 'Unknown')}  \n")
                f.write(f"**Pages:** {', '.join([str(p) for p in card.get('page_reference', [])])}  \n\n")
                f.write("---\n\n")
    
    st.success(f"Generated Storyteller Tactics cards: {cards_path}")
    return cards_path


def display_story_cards(story_cards):
    """Display story cards in the UI with enhanced filtering options"""
    if not story_cards:
        st.info("No story cards available to display")
        return
    
    st.subheader("📚 Storyteller Tactics Cards")
    
    # Extract unique categories, frameworks, and other attributes for filtering
    categories = list(set(card.get("category", "Uncategorized") for card in story_cards))
    frameworks = list(set(card.get("framework", "Unknown") for card in story_cards))
    
    # Normalize sentiments to lowercase and get unique values
    sentiments = list(set(card.get("sentiment", "").lower() for card in story_cards if card.get("sentiment")))
    
    # Collect all keywords and entities for search functionality
    all_keywords = []
    all_entities = []
    for card in story_cards:
        all_keywords.extend(card.get("keywords", []))
        all_entities.extend(card.get("entities", []))
    
    unique_keywords = list(set(all_keywords))
    unique_entities = list(set(all_entities))
    
    # Add filtering options in tabs
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Overview", "🔍 Filters", "⭐ Top Stories", "🔎 Search"])
    
    with tab1:
        # Display enhanced statistics
        st.markdown("### Card Statistics")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Cards", len(story_cards))
        with col2:
            avg_score = sum(card.get("interest_score", 0) for card in story_cards) / len(story_cards) if story_cards else 0
            st.metric("Average Interest Score", f"{avg_score:.1f}/10")
        with col3:
            st.metric("Categories", len(categories))
        
        # Category distribution
        st.markdown("### Category Distribution")
        category_counts = {}
        for card in story_cards:
            cat = card.get("category", "Uncategorized")
            category_counts[cat] = category_counts.get(cat, 0) + 1
        
        # Convert to format for bar chart
        category_data = [{"Category": cat, "Count": count} for cat, count in category_counts.items()]
        
        # Display in a table
        st.dataframe(
            category_data,
            column_config={
                "Category": st.column_config.TextColumn("Category"),
                "Count": st.column_config.ProgressColumn(
                    "Count", 
                    min_value=0, 
                    max_value=max(category_counts.values()),
                    format="%d"
                ),
            },
            hide_index=True,
            use_container_width=True
        )
        
        # Sentiment distribution if available
        if any(card.get("sentiment") for card in story_cards):
            st.markdown("### Sentiment Distribution")
            sentiment_counts = {}
            for card in story_cards:
                # Normalize sentiment to lowercase to avoid duplicates like "positive" and "Positive"
                sentiment = card.get("sentiment", "Not specified").lower()
                if sentiment:
                    # Capitalize first letter for display purposes
                    display_sentiment = sentiment.capitalize()
                    sentiment_counts[display_sentiment] = sentiment_counts.get(display_sentiment, 0) + 1
            
            # Convert to format for visualization
            sentiment_data = [{"Sentiment": sent, "Count": count} for sent, count in sentiment_counts.items()]
            
            # Display in a table
            st.dataframe(
                sentiment_data,
                column_config={
                    "Sentiment": st.column_config.TextColumn("Sentiment"),
                    "Count": st.column_config.ProgressColumn(
                        "Count", 
                        min_value=0, 
                        max_value=max(sentiment_counts.values()),
                        format="%d"
                    ),
                },
                hide_index=True,
                use_container_width=True
            )
    
    with tab2:
        # Enhanced filter controls
        col1, col2 = st.columns(2)
        with col1:
            selected_categories = st.multiselect(
                "Filter by Category",
                options=sorted(categories),
                default=sorted(categories)  # Default to all categories selected
            )
        
        with col2:
            selected_frameworks = st.multiselect(
                "Filter by Framework",
                options=sorted(frameworks),
                default=sorted(frameworks)  # Default to all frameworks selected
            )
        
        # Second row of filters
        col1, col2 = st.columns(2)
        with col1:
            # Interest score filter
            min_score = st.slider("Minimum Interest Score", 1, 10, 1)
        
        with col2:
            # Sentiment filter if sentiment data is available
            if sentiments:
                # Display sentiments with first letter capitalized for better UI
                display_sentiments = [s.capitalize() for s in sorted(sentiments)]
                selected_sentiments_display = st.multiselect(
                    "Filter by Sentiment",
                    options=display_sentiments,
                    default=display_sentiments
                )
                # Convert back to lowercase for filtering
                selected_sentiments = [s.lower() for s in selected_sentiments_display]
        
        # Apply filters
        filtered_cards = story_cards
        if selected_categories:
            filtered_cards = [card for card in filtered_cards if card.get("category") in selected_categories]
        if selected_frameworks:
            filtered_cards = [card for card in filtered_cards if card.get("framework") in selected_frameworks]
        if 'selected_sentiments' in locals() and selected_sentiments:
            # Match lowercase sentiment for filtering
            filtered_cards = [card for card in filtered_cards if card.get("sentiment", "").lower() in selected_sentiments]
        filtered_cards = [card for card in filtered_cards if card.get("interest_score", 0) >= min_score]
        
        # Sort by interest score
        filtered_cards = sorted(filtered_cards, key=lambda x: x.get("interest_score", 0), reverse=True)
        
        # Display cards
        st.write(f"Showing {len(filtered_cards)} of {len(story_cards)} cards")
        
        for i, card in enumerate(filtered_cards):
            with st.expander(f"📖 {card.get('title', f'Card {i+1}')} - {card.get('framework')} ({card.get('interest_score')}/10)"):
                col1, col2 = st.columns([2, 1])
                with col1:
                    st.markdown(f"**Category:** {card.get('category', 'N/A')}")
                    st.markdown(f"**Emotional Core:** {card.get('emotional_core', 'N/A')}")
                    
                    # Display keywords and entities if available
                    if card.get('keywords', []):
                        st.markdown(f"**Keywords:** {', '.join(card.get('keywords', []))}")
                    if card.get('sentiment', ''):
                        st.markdown(f"**Sentiment:** {card.get('sentiment', '')}")
                    
                    st.markdown(f"**Content:**")
                    st.markdown(f"{card.get('content', '')}")
                    st.markdown(f"**Key Insight:** {card.get('key_insight', '')}")
                with col2:
                    st.markdown(f"**Contextual Relevance:**")
                    st.markdown(f"{card.get('contextual_relevance', '')}")
                    st.markdown("**Key Phrases:**")
                    for phrase in card.get('key_phrases', []):
                        st.markdown(f"- \"{phrase}\"")
                    st.markdown(f"**Audience Impact:** {card.get('audience_impact', '')}")
                
                # Display storyteller script with sections
                st.markdown("**Storyteller Script:**")
                
                script_tabs = st.tabs(["Complete Script", "Hook", "Narrative", "Conclusion", "Delivery"])
                
                # Get script sections
                script_hook = card.get('script_hook', '')
                script_narrative = card.get('script_narrative', '')
                script_conclusion = card.get('script_conclusion', '')
                script_delivery = card.get('script_delivery', '')
                
                # If sections are not available, try to parse from full script
                storyteller_script = card.get('storyteller_script', '')
                if not (script_hook and script_narrative and script_conclusion and script_delivery):
                    # Try to parse sections from the full script
                    if "Hook:" in storyteller_script and "Narrative:" in storyteller_script:
                        try:
                            # Extract hook
                            hook_start = storyteller_script.find("Hook:") + 5
                            hook_end = storyteller_script.find("Narrative:")
                            script_hook = storyteller_script[hook_start:hook_end].strip()
                            
                            # Extract narrative
                            narrative_start = storyteller_script.find("Narrative:") + 10
                            narrative_end = storyteller_script.find("Powerful Conclusion:")
                            if narrative_end == -1:  # Try alternative label
                                narrative_end = storyteller_script.find("Conclusion:")
                            script_narrative = storyteller_script[narrative_start:narrative_end].strip()
                            
                            # Extract conclusion
                            conclusion_start = max(storyteller_script.find("Powerful Conclusion:") + 20, 
                                                  storyteller_script.find("Conclusion:") + 11)
                            conclusion_end = storyteller_script.find("Delivery:")
                            script_conclusion = storyteller_script[conclusion_start:conclusion_end].strip()
                            
                            # Extract delivery
                            delivery_start = storyteller_script.find("Delivery:") + 9
                            script_delivery = storyteller_script[delivery_start:].strip()
                        except:
                            # If parsing fails, use the whole script
                            pass
                
                with script_tabs[0]:
                    st.text_area("Complete Script", value=storyteller_script, height=150, key=f"script_{i}", disabled=True)
                with script_tabs[1]:
                    st.text_area("Hook", value=script_hook, height=80, key=f"hook_{i}", disabled=True)
                with script_tabs[2]:
                    st.text_area("Narrative", value=script_narrative, height=150, key=f"narrative_{i}", disabled=True)
                with script_tabs[3]:
                    st.text_area("Conclusion", value=script_conclusion, height=80, key=f"conclusion_{i}", disabled=True)
                with script_tabs[4]:
                    st.text_area("Delivery", value=script_delivery, height=80, key=f"delivery_{i}", disabled=True)
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.markdown(f"**Suitable for:** {', '.join(card.get('audience', ['General audience']))}")
                with col2:
                    st.markdown(f"**Source:** {card.get('source', 'Unknown')}")
                with col3:
                    st.markdown(f"**Pages:** {', '.join([str(p) for p in card.get('page_reference', [])])}")
    
    with tab3:
        # Show top stories by interest score
        top_cards = sorted(story_cards, key=lambda x: x.get("interest_score", 0), reverse=True)[:5]
        
        st.markdown("### Top 5 Stories")
        for i, card in enumerate(top_cards):
            st.markdown(f"#### {i+1}. {card.get('title')} ({card.get('interest_score')}/10)")
            st.markdown(f"*{card.get('emotional_core', 'N/A')}*")
            
            # Display keywords if available
            if card.get('keywords', []):
                keywords_str = ', '.join(card.get('keywords', []))
                st.markdown(f"**Keywords:** {keywords_str}")
            
            st.markdown(card.get('key_insight', ''))
            
            # Display script hook if available
            script_hook = card.get('script_hook', '')
            if script_hook:
                st.markdown(f"**Hook:** *{script_hook}*")
            
            st.markdown(f"Framework: {card.get('framework')}")
            st.markdown("---")
    
    with tab4:
        # Advanced search functionality
        st.markdown("### Search Story Cards")
        search_query = st.text_input("Enter search terms (searches in titles, content, insights, scripts)")
        
        # Add option to search in specific fields
        search_options = st.multiselect(
            "Search in these fields:",
            options=["Title", "Content", "Key Insight", "Storyteller Script", "Keywords", "Entities", "Key Phrases"],
            default=["Title", "Content", "Key Insight", "Storyteller Script"]
        )
        
        # Search in specific keywords/entities
        col1, col2 = st.columns(2)
        with col1:
            if unique_keywords:
                selected_keywords = st.multiselect(
                    "Filter by specific keywords:",
                    options=sorted(unique_keywords)
                )
        with col2:
            if unique_entities:
                selected_entities = st.multiselect(
                    "Filter by specific entities:",
                    options=sorted(unique_entities)
                )
        
        # Perform search if query entered
        if search_query or selected_keywords or selected_entities:
            search_results = []
            
            # Free text search
            if search_query:
                query = search_query.lower()
                for card in story_cards:
                    # Check each selected field
                    match_found = False
                    
                    if "Title" in search_options and query in card.get("title", "").lower():
                        match_found = True
                    elif "Content" in search_options and query in card.get("content", "").lower():
                        match_found = True
                    elif "Key Insight" in search_options and query in card.get("key_insight", "").lower():
                        match_found = True
                    elif "Storyteller Script" in search_options and query in card.get("storyteller_script", "").lower():
                        match_found = True
                    elif "Keywords" in search_options and any(query in kw.lower() for kw in card.get("keywords", [])):
                        match_found = True
                    elif "Entities" in search_options and any(query in ent.lower() for ent in card.get("entities", [])):
                        match_found = True
                    elif "Key Phrases" in search_options and any(query in phrase.lower() for phrase in card.get("key_phrases", [])):
                        match_found = True
                    
                    if match_found:
                        search_results.append(card)
            else:
                # If no text query, include all cards for filtering
                search_results = story_cards.copy()
            
            # Filter by selected keywords and entities
            if 'selected_keywords' in locals() and selected_keywords:
                search_results = [card for card in search_results if 
                                any(kw in card.get("keywords", []) for kw in selected_keywords)]
            
            if 'selected_entities' in locals() and selected_entities:
                search_results = [card for card in search_results if 
                                any(ent in card.get("entities", []) for ent in selected_entities)]
            
            # Display search results
            if search_results:
                st.write(f"Found {len(search_results)} matching cards")
                
                # Sort by interest score
                search_results = sorted(search_results, key=lambda x: x.get("interest_score", 0), reverse=True)
                
                for i, card in enumerate(search_results):
                    with st.expander(f"📖 {card.get('title', f'Result {i+1}')} - {card.get('framework')} ({card.get('interest_score')}/10)"):
                        col1, col2 = st.columns([2, 1])
                        with col1:
                            st.markdown(f"**Category:** {card.get('category', 'N/A')}")
                            st.markdown(f"**Emotional Core:** {card.get('emotional_core', 'N/A')}")
                            if card.get('keywords', []):
                                st.markdown(f"**Keywords:** {', '.join(card.get('keywords', []))}")
                            st.markdown(f"**Content:**")
                            st.markdown(f"{card.get('content', '')}")
                            st.markdown(f"**Key Insight:** {card.get('key_insight', '')}")
                        with col2:
                            st.markdown(f"**Contextual Relevance:**")
                            st.markdown(f"{card.get('contextual_relevance', '')}")
                            st.markdown("**Key Phrases:**")
                            for phrase in card.get('key_phrases', []):
                                st.markdown(f"- \"{phrase}\"")
                            st.markdown(f"**Audience Impact:** {card.get('audience_impact', '')}")
                        
                        # Display storyteller script with hook highlighted
                        st.markdown("**Storyteller Script:**")
                        script_hook = card.get('script_hook', '')
                        if script_hook:
                            st.markdown(f"**Hook:** *{script_hook}*")
                        st.text_area("Full Script", value=card.get('storyteller_script', ''), height=150, key=f"search_script_{i}", disabled=True)
            else:
                st.info("No matching cards found")


def export_to_storyteller_deck(story_cards, pdf_name):
    """Format story cards in a structure compatible with Storyteller Tactics deck"""
    
    # Create directory if it doesn't exist
    export_dir = BASE_DIR / "exports"
    export_dir.mkdir(exist_ok=True)
    
    # Group by category
    categories = {}
    for card in story_cards:
        category = card.get("category", "Uncategorized")
        if category not in categories:
            categories[category] = []
        categories[category].append(card)
    
    # Create markdown file with Storyteller Tactics format
    export_path = export_dir / f"{pdf_name.replace('.pdf', '')}_storyteller_export.md"
    
    with open(export_path, 'w', encoding='utf-8') as f:
        f.write(f"# Storyteller Tactics: {pdf_name}\n\n")
        f.write("📚Recipe\n")
        f.write("Stories extracted from your book can change conversations. See which problems you can solve by using these story cards.\n\n")
        
        # Table of contents
        f.write("## Table of Contents\n\n")
        for category in categories:
            f.write(f"- [{category}](#{category.lower().replace(' ', '-')})\n")
        f.write("\n---\n\n")
        
        for category, cards in categories.items():
            f.write(f"## {category}\n\n")
            if category in STORY_CATEGORIES:
                f.write(f"{STORY_CATEGORIES[category]}\n\n")
            
            for card in cards:
                framework = card.get("framework", "Story Card")
                f.write(f"### {framework}\n")
                f.write(f"_{card.get('title', 'Story Card')}_\n\n")
                
                # Add keywords and entities if available
                if card.get('keywords', []):
                    f.write(f"**Keywords:** {', '.join(card.get('keywords', []))}\n\n")
                    
                f.write(f"**Emotional Core:** {card.get('emotional_core', 'Inspiration')}\n\n")
                f.write(f"{card.get('content', '')}\n\n")
                f.write(f"**Key Insight:** {card.get('key_insight', '')}\n\n")
                f.write(f"**How to use this story:**\n{card.get('contextual_relevance', '')}\n\n")
                
                f.write("**Key Phrases:**\n")
                for phrase in card.get('key_phrases', []):
                    f.write(f"- \"{phrase}\"\n")
                f.write("\n")
                
                f.write(f"**Audience Impact:**\n{card.get('audience_impact', '')}\n\n")
                
                # Get script sections for structured output
                script_hook = card.get('script_hook', '')
                script_narrative = card.get('script_narrative', '')
                script_conclusion = card.get('script_conclusion', '')
                script_delivery = card.get('script_delivery', '')
                
                # If sections are available, use them for a better formatted script
                if script_hook and script_narrative and script_conclusion and script_delivery:
                    f.write(f"**Storyteller Script:**\n```\n")
                    f.write(f"Hook: \n{script_hook}\n\n")
                    f.write(f"Narrative: \n{script_narrative}\n\n")
                    f.write(f"Powerful Conclusion: \n{script_conclusion}\n\n")
                    f.write(f"Delivery: \n{script_delivery}\n")
                    f.write("```\n\n")
                else:
                    # Otherwise use the full script
                    f.write(f"**Storyteller Script:**\n```\n{card.get('storyteller_script', '')}\n```\n\n")
                
                f.write("---\n\n")
    
    st.success(f"Exported to Storyteller Tactics format: {export_path}")
    return export_path


def main():
    st.set_page_config(
        page_title="Storyteller Tactics Card Generator",
        page_icon="🃏",
        layout="wide"
    )
    
    # Apply McKinsey styling
    apply_mckinsey_style()
    
    # Header with McKinsey style
    st.markdown(
        """
        <div style="background-color: #003DA5; padding: 1.5rem; border-radius: 0.5rem; margin-bottom: 1rem;">
            <h1 style="color: white; margin: 0;">Storyteller Tactics Card Generator</h1>
            <p style="color: white; margin-top: 0.5rem;">Transform your content into powerful storytelling assets</p>
        </div>
        """,
        unsafe_allow_html=True
    )
    
    # Initialize session state variables
    if "processing" not in st.session_state:
        st.session_state.processing = False
    
    # Option to resume processing
    if "resume_processing" not in st.session_state:
        st.session_state.resume_processing = False

    # Store selected story elements
    if "selected_story_elements" not in st.session_state:
        st.session_state.selected_story_elements = list(STORY_ELEMENTS.keys())
        
    # Check for API key in environment variables
    if not os.environ.get("GEMINI_API_KEY"):
        st.error("GEMINI_API_KEY environment variable not set. Please set it before running.")
        st.code("export GEMINI_API_KEY=your_api_key_here")
        st.stop()
    
    # Create necessary directories
    setup_directories()
    
    # Sidebar for configuration
    with st.sidebar:
        st.markdown(
            """
            <div style="background-color: #D4E6FC; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                <h3 style="color: #003DA5; margin-top: 0;">Configuration</h3>
            </div>
            """,
            unsafe_allow_html=True
        )
        
        # Models info
        st.info(f"Model: {MODEL}")
        
        # Processing parameters
        st.session_state.chunk_size = st.number_input(
            "Pages per Chunk",
            min_value=1,
            max_value=50,
            value=5,
            help="Number of pages to analyze together for story extraction"
        )
        
        # Add page range selection
        st.markdown("##### Page Range")
        col1, col2 = st.columns(2)
        with col1:
            start_page = st.number_input("Start Page", min_value=1, value=1)
        with col2:
            end_page = st.number_input("End Page", min_value=1, value=999)
        
        st.info("Leave End Page as 999 to process until the end of the document")
        
        # Analysis depth selection
        analysis_depth = st.radio(
            "Analysis Depth",
            options=["Standard", "Deep", "Comprehensive"],
            index=1,
            help="Controls how intensively the AI analyzes the text for stories"
        )
        
        # Story elements to focus on - default all selected
        with st.expander("Story Elements (Advanced)", expanded=False):
            st.markdown("Select the story elements to look for:")
            
            # Create columns for better layout of checkboxes
            element_cols = st.columns(2)
            selected_elements = []
            
            for i, (element, description) in enumerate(STORY_ELEMENTS.items()):
                col_idx = i % 2
                with element_cols[col_idx]:
                    if st.checkbox(element, value=True, help=description, key=f"element_{element}"):
                        selected_elements.append(element)
            
            st.session_state.selected_story_elements = selected_elements
        
        # Storyteller Tactics frameworks to focus on - default all selected
        with st.expander("Story Frameworks", expanded=False):
            st.markdown("Select frameworks to focus on:")
            
            selected_frameworks = []
            for category, frameworks in {
                "Stories that Sell": [fw for fw, cat in FRAMEWORK_CATEGORY_MAP.items() if cat == "Stories that Sell"],
                "Stories that Motivate": [fw for fw, cat in FRAMEWORK_CATEGORY_MAP.items() if cat == "Stories that Motivate"],
                "Stories that Convince": [fw for fw, cat in FRAMEWORK_CATEGORY_MAP.items() if cat == "Stories that Convince"],
                "Stories that Connect": [fw for fw, cat in FRAMEWORK_CATEGORY_MAP.items() if cat == "Stories that Connect"],
                "Stories that Explain": [fw for fw, cat in FRAMEWORK_CATEGORY_MAP.items() if cat == "Stories that Explain"],
                "Stories that Lead": [fw for fw, cat in FRAMEWORK_CATEGORY_MAP.items() if cat == "Stories that Lead"],
                "Stories that Impress": [fw for fw, cat in FRAMEWORK_CATEGORY_MAP.items() if cat == "Stories that Impress"],
            }.items():
                st.markdown(f"**{category}**")
                for framework in frameworks:
                    if st.checkbox(framework, value=True, key=f"framework_{framework}", 
                                 help=STORY_FRAMEWORKS.get(framework, "")):
                        selected_frameworks.append(framework)
                st.markdown("---")
        
        # If no specific frameworks selected, use all
        if not selected_frameworks:
            selected_frameworks = list(STORY_FRAMEWORKS.keys())
        
        st.session_state.preferred_frameworks = selected_frameworks
        
        # Resume option
        st.session_state.resume_processing = st.checkbox(
            "Resume from last position",
            value=False,
            help="Continue processing from where you left off"
        )
        
        # API rate info
        st.info("Note: Gemini API has rate limits. The tool will automatically retry when limits are hit.")
    
    # Main content area with tabs
    tab1, tab2 = st.tabs(["📄 Upload & Process", "📊 Results"])
    
    with tab1:
        # File uploader
        st.markdown("### Upload Your Document")
        uploaded_file = st.file_uploader("Upload PDF", type=["pdf"])
        
        if uploaded_file is not None:
            # Display file info
            try:
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                    tmp_file.write(uploaded_file.getvalue())
                    pdf_path = tmp_file.name
                
                # Quick file check
                pdf_document = fitz.open(pdf_path)
                total_pages = pdf_document.page_count
                pdf_document.close()
                
                # File info card
                st.markdown(
                    f"""
                    <div style="background-color: #F7F7F7; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
                        <h4 style="color: #003DA5; margin-top: 0;">Document Details</h4>
                        <p><strong>Filename:</strong> {uploaded_file.name}<br>
                        <strong>Size:</strong> {uploaded_file.size / 1024:.2f} KB<br>
                        <strong>Pages:</strong> {total_pages}</p>
                    </div>
                    """,
                    unsafe_allow_html=True
                )
                
                # Adjust end_page based on total pages
                if end_page > total_pages:
                    end_page = total_pages
                    st.info(f"End page adjusted to {total_pages} (document's last page)")
            except Exception as e:
                st.error(f"Error opening PDF: {e}")
                st.stop()
            
            # Load existing cards if available
            existing_cards = load_existing_card_set(uploaded_file.name)
            if existing_cards and not st.session_state.processing:
                st.success(f"Found {len(existing_cards)} existing story cards")
                
                # Options for existing cards
                st.markdown("### Options")
                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button("📝 Generate Markdown Deck", use_container_width=True):
                        markdown_path = generate_markdown_cards(existing_cards, uploaded_file.name)
                        if markdown_path:
                            with open(markdown_path, "r", encoding="utf-8") as f:
                                st.download_button(
                                    label="⬇️ Download Storyteller Tactics Deck",
                                    data=f.read(),
                                    file_name=f"{uploaded_file.name.replace('.pdf', '')}_storyteller_cards.md",
                                    mime="text/markdown",
                                    use_container_width=True
                                )
                with col2:
                    if st.button("📋 Export to Storyteller Format", use_container_width=True):
                        export_path = export_to_storyteller_deck(existing_cards, uploaded_file.name)
                        if export_path:
                            with open(export_path, "r", encoding="utf-8") as f:
                                st.download_button(
                                    label="⬇️ Download Storyteller Export",
                                    data=f.read(),
                                    file_name=f"{uploaded_file.name.replace('.pdf', '')}_storyteller_export.md",
                                    mime="text/markdown",
                                    use_container_width=True
                                )
                with col3:
                    if st.button("🔄 Start Fresh Analysis", use_container_width=True):
                        st.session_state.processing = True
            else:
                # Process button 
                if st.button("🚀 Generate Story Cards", use_container_width=True):
                    st.session_state.processing = True
            
            # Process the PDF if button was clicked
            if st.session_state.processing:
                try:
                    # Initialize client
                    client = genai.Client(
                        api_key=os.environ.get("GEMINI_API_KEY"),
                    )
                    
                    # Process PDF in chunks with selected story elements
                    story_cards = process_pdf_chunks(
                        client=client,
                        pdf_path=pdf_path,
                        pdf_name=uploaded_file.name,
                        chunk_size=st.session_state.chunk_size,
                        start_page=start_page,
                        end_page=end_page,
                        story_elements=st.session_state.selected_story_elements
                    )
                    
                    # Update the results tab with extracted cards
                    if story_cards and not st.session_state.pause_processing:
                        st.session_state.story_cards = story_cards
                        
                        # Generate download options
                        st.markdown("### Download Options")
                        col1, col2, col3 = st.columns(3)
                        
                        with col1:
                            # Generate and provide download for markdown deck
                            markdown_path = generate_markdown_cards(story_cards, uploaded_file.name)
                            if markdown_path:
                                with open(markdown_path, "r", encoding="utf-8") as f:
                                    st.download_button(
                                        label="⬇️ Download Markdown Deck",
                                        data=f.read(),
                                        file_name=f"{uploaded_file.name.replace('.pdf', '')}_storyteller_cards.md",
                                        mime="text/markdown",
                                        use_container_width=True
                                    )
                        
                        with col2:
                            # Provide export to Storyteller format
                            export_path = export_to_storyteller_deck(story_cards, uploaded_file.name)
                            if export_path:
                                with open(export_path, "r", encoding="utf-8") as f:
                                    st.download_button(
                                        label="⬇️ Download Storyteller Export",
                                        data=f.read(),
                                        file_name=f"{uploaded_file.name.replace('.pdf', '')}_storyteller_export.md",
                                        mime="text/markdown",
                                        use_container_width=True
                                    )
                        
                        with col3:
                            # Provide download for JSON
                            cards_path = CARD_SETS_DIR / f"{uploaded_file.name.replace('.pdf', '')}_cards.json"
                            if cards_path.exists():
                                with open(cards_path, "r", encoding="utf-8") as f:
                                    st.download_button(
                                        label="⬇️ Download JSON Data",
                                        data=f.read(),
                                        file_name=f"{uploaded_file.name.replace('.pdf', '')}_cards.json",
                                        mime="application/json",
                                        use_container_width=True
                                    )
                        
                        # Suggest viewing results tab
                        st.success("✅ Processing complete! Click on the 'Results' tab to view your story cards.")
                    
                    # Reset processing state only if not paused
                    if not st.session_state.get("pause_processing", False):
                        st.session_state.processing = False
                except Exception as e:
                    st.error(f"Error processing PDF: {e}")
                    st.session_state.processing = False
        else:
            # Upload instructions with McKinsey style
            st.markdown(
                """
                <div style="background-color: #D4E6FC; padding: 1.5rem; border-radius: 0.5rem; text-align: center;">
                    <h3 style="color: #003DA5;">Get Started</h3>
                    <p>Upload a PDF document to extract powerful storytelling elements.</p>
                    <p>The AI will analyze your content and transform it into ready-to-use story cards.</p>
                </div>
                """,
                unsafe_allow_html=True
            )
    
    with tab2:
        # Display cards if available
        if "story_cards" in st.session_state and st.session_state.story_cards:
            display_story_cards(st.session_state.story_cards)
        elif uploaded_file is not None and existing_cards:
            display_story_cards(existing_cards)
        else:
            st.info("No story cards to display yet. Process a document first.")


if __name__ == "__main__":
    main()