from fastapi import <PERSON><PERSON><PERSON>, HTTP<PERSON>xception, Request, UploadFile, File, Query
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import fitz  # PyMuPDF
import openai
import os
import logging

# --- Configuration & Setup ---
# OPENAI_API_KEY will be loaded from system environment variables
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    logging.warning("OPENAI_API_KEY not found in environment variables. Chat functionality will not work.")
    # raise ValueError("OPENAI_API_KEY not found in environment variables.") # Or handle more gracefully

client = openai.OpenAI(api_key=OPENAI_API_KEY)

app = FastAPI(
    title="Storyteller Tactics API",
    description="API for Storyteller Tactics and AI Chat",
    version="0.1.0"
)

# Mount static files directory
# Ensure 'static' directory exists at the root of your project (c:\0000_Proj\story0101\static)
# We will create index.html and script.js in this directory later.
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except RuntimeError as e:
    logging.error(f"Could not mount static directory: {e}. Ensure 'static' directory exists.")
    # Create the directory if it doesn't exist to prevent app crash on startup
    if not os.path.exists("static"):
        os.makedirs("static")
        logging.info("Created 'static' directory.")
        app.mount("/static", StaticFiles(directory="static"), name="static")

# --- Storyteller Tactics Data (from ccc7.py) ---
STORY_CATEGORIES = {
    "Stories that Sell": "Build trust and convince people of your ability to deliver. Trust is essential for selling, more than just price or quality.",
    "Stories that Motivate": "Inspire people to support your ideas and take action. Show your plan and your underlying motivations.",
    "Stories that Convince": "Explain complex information to a non-expert audience and build trust in your judgment. Avoid overwhelming with data; focus on understanding and trust.",
    "Stories that Connect": "Foster empathy and understanding by showing different perspectives. Stories help people see things from another point of view.",
    "Stories that Explain": "Make abstract strategies understandable and relevant. Bring your strategic direction to life.",
    "Stories that Lead": "Build a stronger and more cohesive team. Share stories of struggles, triumphs, and learnings within the team.",
    "Stories that Impress": "Present ideas confidently and clearly. Avoid jargon and use storytelling techniques to engage your audience."
}

STORY_FRAMEWORKS = {
    # Stories that Sell
    "Simple Sales Stories": "Share relatable success stories of helping others. Use stories about existing customers to convince new ones.",
    "Social Proof": "Use trends, prototypes, and testimonials to strengthen your case.",
    "Rags to Riches": "Tell optimistic stories with the customer as the central figure. A story of rising to success from humble beginnings.",
    "Pitch Perfect": "Condense your message into a concise elevator pitch. Craft compelling pitches using Problem, Solution, Trust formula.",
    "Audience Profile": "Understand your audience and their problems to create targeted sales stories.",
    
    # Stories that Motivate
    "Dragon & the City": "Explain your overarching goals and vision. Frame projects as overcoming a threat to improve the status quo.",
    "Drive Stories": "Articulate your motivations and connect them with your audience's potential motivations.",
    "Three Great Conflicts": "Identify and address the obstacles you need to overcome through fundamental human struggles.",
    "Innovation Curve": "Reassure your audience about the risks involved in your idea based on their risk tolerance.",
    "No Easy Way": "Provide a realistic outlook on the challenges ahead. Acknowledges setbacks on the path to success.",
    
    # Stories that Convince
    "Three is the Magic Number": "Prioritize key facts your audience can remember using patterns of three.",
    "That's Funny": "Share the excitement and insights behind your discoveries. Find unexpected moments that reveal insights.",
    "Data Detectives": "Present data in a narrative format to improve understanding and recall.",
    "Trust Me, I'm an Expert": "Demonstrate your credibility and expertise by showing your values through stories of your actions.",
    "Hero & Guide": "Position yourself as the expert guide assisting your audience. Your user is the hero; you are the guide.",
    
    # Stories that Connect
    "Story Listening": "Understand others by listening to their experiences and stories.",
    "Abstractions": "Observe behavior in addition to asking questions to understand deeper knowledge.",
    "Universal Stories": "Find common ground and shared experiences that resonate across cultures.",
    "Story-ish Conversations": "Look for stories in everyday interactions to uncover insights about people and change.",
    "Circle of Life": "Develop relatable stories based on universal characters and life stages.",
    
    # Stories that Explain
    "Order & Chaos": "Show where your strategy fits in a changing world. Balance between the known and unknown.",
    "Good & Evil": "Define the important battles your strategy addresses through moral conflicts and choices.",
    "What's it About?": "Explain the relevance of the strategy to your colleagues by focusing on change and benefit.",
    "Rolls Royce Moment": "Illustrate your strategy in action with a vivid, exemplary detail that represents the whole story.",
    "Story Hooks": "Make your strategy sound engaging with compelling openings that capture attention.",
    
    # Stories that Lead
    "Curious Tales": "Discover what motivates your team members by exploring what grabs attention.",
    "Man in a Hole": "Frame teamwork as an epic journey with challenges, crisis, and recovery with newfound wisdom.",
    "Emotional Dashboard": "Find stories in the highs and lows of projects by identifying strong emotions.",
    "Thoughtful Failures": "Extract lessons from mistakes by analyzing goals, assumptions, insights, skills, and communication.",
    "Story Bank": "Collect and share valuable team stories for future reference and learning.",
    
    # Stories that Impress
    "Movie Time": "Tell a story, not just present facts. Use vivid descriptions to create a mental movie for your audience.",
    "Five Ts": "Structure your story effectively using Timeline, Turning Points, Tensions, Temptations, Teachable Moments.",
    "Show and Tell": "Make visuals and narration work together effectively to maintain audience attention.",
    "Cut to the Chase": "Have a backup plan if your presentation falters. Regain audience attention by focusing on Action, Emotion, or Meaning.",
    "Secrets & Puzzles": "Engage the audience by hinting at undiscovered information or anomalies to create intrigue."
}

FRAMEWORK_CATEGORY_MAP = {
    "Simple Sales Stories": "Stories that Sell", "Social Proof": "Stories that Sell", "Rags to Riches": "Stories that Sell", "Pitch Perfect": "Stories that Sell", "Audience Profile": "Stories that Sell",
    "Dragon & the City": "Stories that Motivate", "Drive Stories": "Stories that Motivate", "Three Great Conflicts": "Stories that Motivate", "Innovation Curve": "Stories that Motivate", "No Easy Way": "Stories that Motivate",
    "Three is the Magic Number": "Stories that Convince", "That's Funny": "Stories that Convince", "Data Detectives": "Stories that Convince", "Trust Me, I'm an Expert": "Stories that Convince", "Hero & Guide": "Stories that Convince",
    "Story Listening": "Stories that Connect", "Abstractions": "Stories that Connect", "Universal Stories": "Stories that Connect", "Story-ish Conversations": "Stories that Connect", "Circle of Life": "Stories that Connect",
    "Order & Chaos": "Stories that Explain", "Good & Evil": "Stories that Explain", "What's it About?": "Stories that Explain", "Rolls Royce Moment": "Stories that Explain", "Story Hooks": "Stories that Explain",
    "Curious Tales": "Stories that Lead", "Man in a Hole": "Stories that Lead", "Emotional Dashboard": "Stories that Lead", "Thoughtful Failures": "Stories that Lead", "Story Bank": "Stories that Lead",
    "Movie Time": "Stories that Impress", "Five Ts": "Stories that Impress", "Show and Tell": "Stories that Impress", "Cut to the Chase": "Stories that Impress", "Secrets & Puzzles": "Stories that Impress"
}

# --- Pydantic Models ---
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: str = "o4-mini" # Default model
    # Add other parameters like temperature, max_tokens if needed

class ChatResponse(BaseModel):
    reply: str
    # Add other response fields like usage, finish_reason if needed

class Framework(BaseModel):
    name: str
    description: str

class TacticCategory(BaseModel):
    category_name: str
    category_description: str
    frameworks: List[Framework]

class StoryCard(BaseModel):
    title: str
    framework: str
    category: str
    emotional_core: str
    content: str
    key_insight: str
    contextual_relevance: str
    storyteller_script: str
    script_hook: str = ""
    script_narrative: str = ""
    script_conclusion: str = ""
    script_delivery: str = ""
    key_phrases: List[str]
    audience_impact: str
    interest_score: int
    audience: List[str]
    source: str
    page_reference: List[int]
    entities: List[str] = []
    sentiment: str = ""
    keywords: List[str] = []

class StoryChunk(BaseModel):
    pages: List[int]
    text: str
    has_story_potential: bool = False
    story_frameworks: List[str] = []
    entities: List[str] = []
    sentiment: str = ""
    keywords: List[str] = []

def prepare_text_chunks(pdf_document: fitz.Document, chunk_size: int) -> List[StoryChunk]:
    """Divide the PDF into multi-page chunks for story extraction"""
    chunks = []
    total_pages = pdf_document.page_count
    
    for start_page in range(0, total_pages, chunk_size):
        end_page = min(start_page + chunk_size, total_pages)
        
        # Extract text from this chunk of pages
        chunk_text = ""
        page_numbers = []
        for page_num in range(start_page, end_page):
            page = pdf_document[page_num]
            page_text = page.get_text()
            chunk_text += f"\n\n--- PAGE {page_num + 1} ---\n\n{page_text}"
            page_numbers.append(page_num + 1)
        
        # Create a chunk object
        chunk = StoryChunk(
            pages=page_numbers,
            text=chunk_text
        )
        chunks.append(chunk)
    
    return chunks

async def analyze_story_potential(chunk: StoryChunk, framework_names: List[str]) -> StoryChunk:
    """Analyzes a text chunk for story potential using OpenAI model."""
    framework_descriptions = []
    for fw_name in framework_names:
        if fw_name in STORY_FRAMEWORKS:
            framework_descriptions.append(f"- {fw_name}: {STORY_FRAMEWORKS[fw_name]}")
    
    framework_section = "\n".join(framework_descriptions)

    system_prompt = f"""
Analyze the following text chunk from a document to determine its storytelling potential. Your response MUST be a valid JSON object.

Consider the following storytelling frameworks and their descriptions:
{framework_section}

Your analysis should include:
1.  `has_story_potential` (boolean): True if the chunk contains compelling narrative elements, otherwise false.
2.  `story_frameworks` (list of strings): A list of framework names from the provided list that are most relevant to this chunk. Return an empty list if none are relevant.
3.  `sentiment` (string): The overall emotional tone of the chunk (e.g., positive, negative, neutral, mixed, inspirational, cautionary).
4.  `entities` (list of strings): Key named entities (people, organizations, locations, specific concepts) found in the chunk.
5.  `keywords` (list of strings): Main themes or topics discussed in the chunk.
6.  `reasoning` (string): A brief explanation for your assessment, especially why it has story potential and fits certain frameworks.

JSON output format:
{{ "has_story_potential": boolean, "story_frameworks": [], "sentiment": "", "entities": [], "keywords": [], "reasoning": "" }}
"""

    user_message = f"Text chunk to analyze:\n\n```text\n{chunk.text[:15000]} \n```" # Limit chunk size for context window

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_message}
    ]

    try:
        if not OPENAI_API_KEY:
            logging.error("OpenAI API key not configured. Cannot analyze story potential.")
            # Return chunk unmodified or raise specific error
            return chunk

        response = await client.chat.completions.create(
            model="o4-mini", # Or allow model to be passed as param
            messages=messages,
            temperature=0.3, # Slightly creative but mostly factual
            max_tokens=1024, # Adjust as needed for expected JSON size
            response_format={"type": "json_object"}
        )
        
        response_content = response.choices[0].message.content
        if response_content:
            analysis_result = json.loads(response_content)
            
            chunk.has_story_potential = analysis_result.get("has_story_potential", False)
            chunk.story_frameworks = analysis_result.get("story_frameworks", [])
            chunk.sentiment = analysis_result.get("sentiment", "")
            chunk.entities = analysis_result.get("entities", [])
            chunk.keywords = analysis_result.get("keywords", [])
            # We could also store the 'reasoning' if the StoryChunk model is updated
        else:
            logging.error("Received empty response from OpenAI for story potential analysis.")

    except json.JSONDecodeError as e:
        logging.error(f"JSONDecodeError analyzing story potential: {e}. Response: {response_content}")
    except openai.APIError as e:
        logging.error(f"OpenAI APIError analyzing story potential: {e}")
    except Exception as e:
        logging.error(f"Unexpected error analyzing story potential: {e}")
        # Optionally re-raise or handle more gracefully

    return chunk

async def extract_story_cards(chunk: StoryChunk) -> List[StoryCard]:
    """Extracts detailed StoryCard objects from an analyzed text chunk using OpenAI model."""
    if not chunk.has_story_potential or not chunk.story_frameworks:
        return []

    # Prepare framework descriptions for the prompt
    framework_details_for_prompt = []
    for fw_name in chunk.story_frameworks:
        if fw_name in STORY_FRAMEWORKS:
            framework_details_for_prompt.append(f"- {fw_name}: {STORY_FRAMEWORKS[fw_name]}")
    
    if not framework_details_for_prompt:
        logging.warning(f"No valid framework descriptions found for chunk from pages {chunk.pages}. Skipping card extraction.")
        return []
    
    framework_section = "\n".join(framework_details_for_prompt)

    # Context from previous analysis step
    analysis_context = f"""
Preliminary analysis of this text chunk suggests:
- Identified Story Frameworks: {', '.join(chunk.story_frameworks)}
- Key Entities: {', '.join(chunk.entities)}
- Main Keywords/Themes: {', '.join(chunk.keywords)}
- Overall Sentiment: {chunk.sentiment}
Use this information to generate richer and more targeted story cards.
"""

    system_prompt = f"""
You are an expert in identifying and structuring compelling stories based on the Storyteller Tactics methodology. Your task is to extract one or more detailed 'Story Cards' from the provided text chunk. Your response MUST be a valid JSON object with a single top-level key "story_cards" which contains a list of story card objects.

Text Chunk Context:
{analysis_context}

For EACH story you identify in the text chunk, create a story card object with the following fields:

1.  `title` (string): A concise, compelling title for the story (max 10 words).
2.  `framework` (string): ONE storytelling framework (from the 'Identified Story Frameworks' list above) that this specific story best fits.
3.  `emotional_core` (string): The primary emotion this story evokes (e.g., inspiration, hope, urgency, empathy, curiosity).
4.  `content` (string): A rich summary of the story, including key events, characters, and context. Make it engaging.
5.  `key_insight` (string): The central message, lesson, or revelation from this story.
6.  `contextual_relevance` (string): Briefly explain when or in what situations this story would be most effective to share.
7.  `script_hook` (string): An attention-grabbing opening line or question for telling this story.
8.  `script_narrative` (string): The main body of the story, concisely told with key details.
9.  `script_conclusion` (string): An impactful closing that delivers the main point or a call to action.
10. `script_delivery` (string): Brief guidance on tone, pacing, or emphasis for delivering this story.
11. `key_phrases` (list of strings): 3-5 memorable short phrases or powerful quotes from or about the story.
12. `audience_impact` (string): How this story is likely to affect the audience emotionally and intellectually.
13. `audience` (list of strings): Suggested target audiences (e.g., "general", "leadership team", "new clients", "technical staff").
14. `interest_score` (integer): Your rating (1-10) of how engaging this story would be in a typical conversation or presentation.
15. `source` (string): If identifiable from the text, the original author or document title. Otherwise, use "Unknown".

Focus on these Storytelling Frameworks for this task:
{framework_section}

Example of a single story card object structure (DO NOT use this example data, generate new based on the text):
{{ "title": "The Unexpected Discovery", "framework": "That's Funny", ..., "interest_score": 8, "source": "Document X" }}

Ensure your entire response is a single JSON object: {{ "story_cards": [ {{...card1...}}, {{...card2...}} ] }}
"""

    user_message = f"Text chunk to extract story cards from (pages {chunk.pages[0]}-{chunk.pages[-1]}):\n\n```text\n{chunk.text[:15000]} \n```"

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_message}
    ]

    generated_story_cards: List[StoryCard] = []

    try:
        if not OPENAI_API_KEY:
            logging.error("OpenAI API key not configured. Cannot extract story cards.")
            return []

        response = await client.chat.completions.create(
            model="o4-mini",
            messages=messages,
            temperature=0.6, # Balanced creativity for storytelling
            max_tokens=3000, # Allow for multiple detailed cards
            response_format={"type": "json_object"}
        )
        
        response_content = response.choices[0].message.content
        if response_content:
            response_data = json.loads(response_content)
            raw_cards = response_data.get("story_cards", [])
            
            for card_data in raw_cards:
                try:
                    # Ensure category is derived
                    framework_name = card_data.get("framework")
                    category_name = "Unknown"
                    if framework_name and framework_name in FRAMEWORK_CATEGORY_MAP:
                        category_name = FRAMEWORK_CATEGORY_MAP[framework_name]
                    card_data["category"] = category_name
                    card_data["page_reference"] = chunk.pages

                    # Construct the full storyteller_script from components
                    hook = card_data.get("script_hook", "")
                    narrative = card_data.get("script_narrative", "")
                    conclusion = card_data.get("script_conclusion", "")
                    delivery = card_data.get("script_delivery", "")
                    card_data["storyteller_script"] = f"Hook: {hook}\n\nNarrative: {narrative}\n\nPowerful Conclusion: {conclusion}\n\nDelivery: {delivery}"

                    # Create StoryCard instance (Pydantic will validate and use defaults)
                    story_card = StoryCard(**card_data)
                    generated_story_cards.append(story_card)
                except Exception as e:
                    logging.error(f"Error processing a story card: {e}. Card data: {card_data}")
        else:
            logging.error("Received empty response from OpenAI for story card extraction.")

    except json.JSONDecodeError as e:
        logging.error(f"JSONDecodeError extracting story cards: {e}. Response: {response_content}")
    except openai.APIError as e:
        logging.error(f"OpenAI APIError extracting story cards: {e}")
    except Exception as e:
        logging.error(f"Unexpected error extracting story cards: {e}")

    return generated_story_cards

@app.post("/api/process-pdf", response_model=List[StoryCard])
async def process_pdf_upload(file: UploadFile = File(...), chunk_size: Optional[int] = Query(5, description="Number of pages per chunk for analysis", ge=1, le=10)) -> List[StoryCard]:
    """Processes an uploaded PDF, extracts text, analyzes for story potential, and generates story cards."""
    if not OPENAI_API_KEY:
        raise HTTPException(status_code=500, detail="OpenAI API key not configured. Cannot process PDF.")

    all_story_cards: List[StoryCard] = []
    
    try:
        # Read the uploaded file content
        pdf_content = await file.read()
        
        # PyMuPDF's fitz.open can take bytes directly
        pdf_document = fitz.open(stream=pdf_content, filetype="pdf")
        
        logging.info(f"Processing PDF: {file.filename}, Pages: {pdf_document.page_count}, Chunk size: {chunk_size}")
        
        story_chunks = prepare_text_chunks(pdf_document, chunk_size)
        pdf_document.close() # Close the document after preparing chunks
        
        logging.info(f"Prepared {len(story_chunks)} chunks for analysis.")
        
        # Get all framework names once
        all_framework_names = list(STORY_FRAMEWORKS.keys())

        for i, chunk in enumerate(story_chunks):
            logging.info(f"Analyzing potential of chunk {i+1}/{len(story_chunks)} (pages {chunk.pages[0]}-{chunk.pages[-1]})...")
            analyzed_chunk = await analyze_story_potential(chunk, all_framework_names)
            
            if analyzed_chunk.has_story_potential and analyzed_chunk.story_frameworks:
                logging.info(f"Chunk {i+1} has story potential. Identified frameworks: {analyzed_chunk.story_frameworks}. Extracting cards...")
                cards_from_chunk = await extract_story_cards(analyzed_chunk)
                if cards_from_chunk:
                    all_story_cards.extend(cards_from_chunk)
                    logging.info(f"Extracted {len(cards_from_chunk)} cards from chunk {i+1}.")
            else:
                logging.info(f"Chunk {i+1} does not have significant story potential or no relevant frameworks identified.")

        logging.info(f"Finished processing PDF. Total story cards generated: {len(all_story_cards)}")
        return all_story_cards

    except fitz.FitzError as e:
        logging.error(f"PyMuPDF (Fitz) error processing PDF {file.filename}: {e}")
        raise HTTPException(status_code=400, detail=f"Error processing PDF file: {e}. Ensure it's a valid PDF.")
    except Exception as e:
        logging.error(f"Unexpected error processing PDF {file.filename}: {e}")
        # Log the full traceback for debugging
        import traceback
        logging.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred while processing the PDF: {e}")
    finally:
        await file.close()

# --- API Endpoints ---

@app.get("/", response_class=HTMLResponse, include_in_schema=False)
async def get_index(request: Request):
    index_path = os.path.join("static", "index.html")
    if not os.path.exists(index_path):
        # Fallback content if index.html doesn't exist yet
        return HTMLResponse(content="<html><body><h1>Welcome to Storyteller Tactics</h1><p>Frontend not yet fully deployed. Please check back.</p></body></html>", status_code=200)
    with open(index_path, "r") as f:
        html_content = f.read()
    return HTMLResponse(content=html_content)

@app.get("/api/story/categories", response_model=Dict[str, str])
async def get_story_categories():
    return STORY_CATEGORIES

@app.get("/api/story/frameworks", response_model=Dict[str, str])
async def get_story_frameworks():
    return STORY_FRAMEWORKS

@app.get("/api/story/framework-category-map", response_model=Dict[str, str])
async def get_framework_category_map():
    return FRAMEWORK_CATEGORY_MAP

@app.get("/api/story/tactics", response_model=List[TacticCategory])
async def get_story_tactics():
    tactics_by_category: Dict[str, TacticCategory] = {}
    for category_name, category_desc in STORY_CATEGORIES.items():
        tactics_by_category[category_name] = TacticCategory(
            category_name=category_name,
            category_description=category_desc,
            frameworks=[]
        )
    
    for framework_name, framework_desc in STORY_FRAMEWORKS.items():
        category_name = FRAMEWORK_CATEGORY_MAP.get(framework_name)
        if category_name and category_name in tactics_by_category:
            tactics_by_category[category_name].frameworks.append(
                Framework(name=framework_name, description=framework_desc)
            )
        else:
            logging.warning(f"Framework '{framework_name}' has an unmapped or unknown category: '{category_name}'.")
            # Optionally, add to a default/uncategorized list

    return list(tactics_by_category.values())

@app.post("/api/chat", response_model=ChatResponse)
async def chat_with_model(chat_request: ChatRequest):
    if not OPENAI_API_KEY:
        raise HTTPException(status_code=500, detail="OpenAI API key not configured. Cannot process chat request.")
    try:
        # Convert Pydantic models to dicts for the OpenAI client
        messages_for_api = [msg.dict() for msg in chat_request.messages]

        response = client.chat.completions.create(
            model=chat_request.model, # Use model from request, defaults to o4-mini
            messages=messages_for_api,
            response_format={"type": "text"}, # As per user's example
            # reasoning_effort="medium" # This might be specific to a different client or model version
        )
        
        reply_content = response.choices[0].message.content
        if reply_content is None:
            reply_content = "Sorry, I couldn't generate a response."
            
        return ChatResponse(reply=reply_content)
    except openai.APIError as e:
        logging.error(f"OpenAI API Error: {e}")
        raise HTTPException(status_code=500, detail=f"An error occurred with the AI service: {e}")
    except Exception as e:
        logging.error(f"Unexpected error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {e}")

# --- Main Execution (for uvicorn) ---
if __name__ == "__main__":
    import uvicorn
    # Ensure the .env file is in the same directory as main.py or specify path for load_dotenv()
    # Example: uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
    # The command 'uvicorn main:app --reload' from README.md is preferred for development.
    print("To run this application, use the command from README.md: uvicorn main:app --reload")
