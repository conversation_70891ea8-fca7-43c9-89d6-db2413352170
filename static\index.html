<!DOCTYPE html>
<html lang="en" data-theme="lofi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storyteller Tactics Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.10.1/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        #chatContainer ::-webkit-scrollbar, #storyTacticsContainer ::-webkit-scrollbar {
            width: 8px;
        }
        #chatContainer ::-webkit-scrollbar-thumb, #storyTacticsContainer ::-webkit-scrollbar-thumb {
            background-color: #a0a0a0; /* Lofi scrollbar color */
            border-radius: 4px;
        }
        #chatContainer ::-webkit-scrollbar-track, #storyTacticsContainer ::-webkit-scrollbar-track {
            background-color: #f1f1f1; /* Lofi scrollbar track */
        }
        .message {
            opacity: 0;
            transform: translateY(10px);
            /* transition: opacity 0.3s ease-out, transform 0.3s ease-out; */ /* Handled by anime.js */
        }
        .message.user-message {
            background-color: hsl(var(--n) / var(--tw-bg-opacity, 1)); /* Neutral color from DaisyUI */
            color: hsl(var(--nc) / var(--tw-text-opacity, 1));
            align-self: flex-end;
        }
        .message.ai-message {
            background-color: hsl(var(--b2) / var(--tw-bg-opacity, 1)); /* Base-200 for AI messages */
            color: hsl(var(--bc) / var(--tw-text-opacity, 1));
            align-self: flex-start;
        }
        .framework-btn.selected {
            background-color: hsl(var(--p) / var(--tw-bg-opacity, 1)) !important; /* Primary color */
            color: hsl(var(--pc) / var(--tw-text-opacity, 1)) !important;
            font-weight: bold;
        }
        .collapse-title {
            cursor: pointer;
        }
    </style>
</head>
<body class="bg-base-100 text-base-content min-h-screen flex flex-col items-center p-4 md:p-8">

    <div class="container mx-auto max-w-5xl flex flex-col lg:flex-row gap-6">

        <!-- Story Tactics Section -->
        <aside class="lg:w-1/3 bg-base-200 p-6 rounded-lg shadow-md flex flex-col">
            <h2 class="text-2xl font-semibold mb-4 text-center text-accent">Storyteller Tactics</h2>
            <div id="storyTacticsContainer" class="overflow-y-auto flex-grow h-64 lg:h-[calc(100vh-200px)] pr-2">
                <!-- Tactics will be loaded here by script.js -->
                <div class="text-center">
                    <span class="loading loading-ring loading-lg text-primary"></span>
                    <p>Loading tactics...</p>
                </div>
            </div>
        </aside>

        <!-- Chat Section -->
        <main class="lg:w-2/3 bg-base-200 p-6 rounded-lg shadow-md flex flex-col h-[calc(100vh-100px)] lg:h-auto">
            <header class="text-center mb-2">
                <h1 class="text-3xl font-bold text-primary">AI Chat</h1>
            </header>
            
            <div id="chatContainer" class="flex-grow overflow-y-auto mb-4 p-4 bg-base-100 rounded-md border border-base-300 min-h-[300px]">
                <!-- Chat messages will appear here -->
                 <div class="text-center text-neutral-content">
                    <p>Ask me about Storyteller Tactics or any other topic!</p>
                    <p>Click on a framework to learn more.</p>
                </div>
            </div>

            <!-- PDF Processing Section -->
            <div class="collapse collapse-arrow bg-base-100 border border-base-300 mb-4">
                <input type="checkbox" id="pdf-processor-toggle" class="peer" /> 
                <label for="pdf-processor-toggle" class="collapse-title text-xl font-medium text-secondary peer-checked:text-primary-focus">
                    Process PDF for Story Cards
                </label>
                <div class="collapse-content">
                    <div class="form-control w-full mb-4">
                        <label for="pdfFile" class="label">
                            <span class="label-text">Upload your PDF document:</span>
                        </label>
                        <input type="file" id="pdfFile" accept=".pdf" class="file-input file-input-bordered file-input-primary w-full" />
                    </div>
                    <button id="processPdfButton" class="btn btn-secondary w-full mb-4">Process PDF</button>
                    <div id="pdfProcessStatus" class="text-sm text-info mb-2"></div>
                    <h3 class="text-lg font-semibold mb-2 text-accent">Generated Story Cards:</h3>
                    <div id="storyCardsOutput" class="space-y-4 max-h-96 overflow-y-auto p-2 bg-base-200 rounded">
                        <!-- Story cards will be displayed here -->
                        <p class="text-neutral-content italic">No PDF processed yet, or no story cards found.</p>
                    </div>
                </div>
            </div>
            <!-- End PDF Processing Section -->

            <form id="chatForm" class="flex gap-2 items-center">
                <input type="text" id="chatInput" placeholder="Type your message or select a framework..." class="input input-bordered w-full focus:input-primary" />
                <button type="submit" class="btn btn-primary">
                    Send
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5"><path d="M3.105 3.105a1.5 1.5 0 012.121-.001L18.06 14.894a1.5 1.5 0 01-.001 2.121l-1.559 1.559a1.5 1.5 0 01-2.121-.001L3.105 7.353a1.5 1.5 0 01-.001-2.121L4.664 3.674a1.5 1.5 0 01-.001-2.121L3.105 3.105zM4.518 5.995a.75.75 0 00-1.06 0L2.22 7.233a.75.75 0 000 1.06l1.238 1.238a.75.75 0 001.06 0l1.238-1.238a.75.75 0 000-1.06L4.518 5.995z" /><path d="M14.895 3.105a1.5 1.5 0 012.121.001l1.559 1.559a1.5 1.5 0 01.001 2.121L7.353 18.06a1.5 1.5 0 01-2.121.001L3.674 16.5a1.5 1.5 0 01.001-2.121l1.558-1.559a1.5 1.5 0 012.121-.001L14.895 3.105zM14.082 14.082a.75.75 0 00-1.06 0L11.785 15.32a.75.75 0 000 1.06l1.237 1.238a.75.75 0 001.06 0l1.238-1.238a.75.75 0 000-1.06l-1.238-1.238z" /></svg>
                </button>
            </form>
            <p id="apiKeyWarning" class="text-error text-xs mt-2 text-center hidden">Warning: OPENAI_API_KEY not detected. Chat functionality might be limited.</p>
        </main>

    </div>

    <script src="/static/script.js"></script>
</body>
</html>
