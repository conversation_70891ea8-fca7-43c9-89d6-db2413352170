document.addEventListener('DOMContentLoaded', () => {
    const storyTacticsContainer = document.getElementById('storyTacticsContainer');
    const chatForm = document.getElementById('chatForm');
    const chatInput = document.getElementById('chatInput');
    const chatContainer = document.getElementById('chatContainer');
    const apiKeyWarning = document.getElementById('apiKeyWarning');

    // PDF Processing Elements
    const pdfFileInput = document.getElementById('pdfFile');
    const processPdfButton = document.getElementById('processPdfButton');
    const pdfProcessStatus = document.getElementById('pdfProcessStatus');
    const storyCardsOutput = document.getElementById('storyCardsOutput');
    const pdfProcessorToggle = document.getElementById('pdf-processor-toggle');

    let messages = []; // To store the history of the conversation
    let selectedFrameworkButton = null;

    // --- Fetch and Display Story Tactics ---
    async function fetchStoryTactics() {
        try {
            const response = await fetch('/api/story/tactics');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const tactics = await response.json();
            displayStoryTactics(tactics);
        } catch (error) {
            console.error('Error fetching story tactics:', error);
            storyTacticsContainer.innerHTML = '<p class="text-error">Failed to load story tactics. Please try refreshing.</p>';
        }
    }

    function displayStoryTactics(tactics) {
        storyTacticsContainer.innerHTML = ''; // Clear loading indicator
        if (!tactics || tactics.length === 0) {
            storyTacticsContainer.innerHTML = '<p>No tactics available.</p>';
            return;
        }

        tactics.forEach(category => {
            const collapseDiv = document.createElement('div');
            collapseDiv.className = 'collapse collapse-arrow bg-base-100 mb-2 shadow';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.name = `tactic-category-${category.category_name.replace(/\s+/g, '-')}`;
            checkbox.className = 'peer'; // For DaisyUI collapse state
            // checkbox.checked = true; // Optionally open all by default

            const collapseTitle = document.createElement('div');
            collapseTitle.className = 'collapse-title text-xl font-medium text-primary-focus peer-checked:bg-primary peer-checked:text-primary-content';
            collapseTitle.textContent = category.category_name;
            
            const collapseContent = document.createElement('div');
            collapseContent.className = 'collapse-content bg-base-100 peer-checked:bg-base-200 peer-checked:text-base-content';
            
            const frameworkList = document.createElement('ul');
            frameworkList.className = 'menu p-2';

            category.frameworks.forEach(framework => {
                const listItem = document.createElement('li');
                const frameworkBtn = document.createElement('button');
                frameworkBtn.className = 'btn btn-sm btn-ghost justify-start framework-btn w-full text-left mb-1 hover:bg-primary hover:text-primary-content';
                frameworkBtn.textContent = framework.name;
                frameworkBtn.setAttribute('data-framework-name', framework.name);
                frameworkBtn.setAttribute('data-framework-description', framework.description);

                frameworkBtn.addEventListener('click', () => {
                    chatInput.value = `Tell me more about the "${framework.name}" framework.`;
                    chatInput.focus();
                    
                    // Highlight selected framework
                    if (selectedFrameworkButton) {
                        selectedFrameworkButton.classList.remove('selected', 'btn-active');
                    }
                    frameworkBtn.classList.add('selected', 'btn-active');
                    selectedFrameworkButton = frameworkBtn;
                });
                listItem.appendChild(frameworkBtn);
                frameworkList.appendChild(listItem);
            });

            collapseContent.appendChild(frameworkList);
            collapseDiv.appendChild(checkbox);
            collapseDiv.appendChild(collapseTitle);
            collapseDiv.appendChild(collapseContent);
            storyTacticsContainer.appendChild(collapseDiv);
        });
    }

    // --- Chat Functionality ---
    chatForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        const userInput = chatInput.value.trim();
        if (!userInput) return;

        // Clear initial message if it's the first user message
        if (messages.length === 0 && chatContainer.children.length === 1 && chatContainer.firstElementChild.tagName === 'DIV') {
            chatContainer.innerHTML = ''; 
        }

        addMessageToChat('user', userInput);
        messages.push({ role: 'user', content: userInput });
        chatInput.value = '';
        if (selectedFrameworkButton) {
            selectedFrameworkButton.classList.remove('selected', 'btn-active');
            selectedFrameworkButton = null;
        }

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ messages: messages }) // Send entire history
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            addMessageToChat('ai', data.reply);
            messages.push({ role: 'assistant', content: data.reply });

        } catch (error) {
            console.error('Error sending chat message:', error);
            addMessageToChat('ai', `Error: ${error.message}. The AI assistant might be unavailable.`);
            if (error.message.includes("OpenAI API key not configured")) {
                apiKeyWarning.classList.remove('hidden');
            }
        }
    });

    function addMessageToChat(role, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-bubble message ${role === 'user' ? 'user-message chat-bubble-primary' : 'ai-message chat-bubble-secondary'} mb-2 p-3 rounded-lg max-w-[80%] break-words`;
        messageDiv.textContent = text;
        
        // Determine alignment based on role
        const messageWrapper = document.createElement('div');
        messageWrapper.className = `chat ${role === 'user' ? 'chat-end' : 'chat-start'}`;
        messageWrapper.appendChild(messageDiv);
        
        chatContainer.appendChild(messageWrapper);

        // Animation with anime.js
        anime({
            targets: messageDiv,
            opacity: [0, 1],
            translateY: [10, 0],
            duration: 500,
            easing: 'easeOutQuad'
        });

        chatContainer.scrollTop = chatContainer.scrollHeight; // Scroll to bottom
    }

    // --- Initializations ---
    fetchStoryTactics();

    // Check for API key warning (can be more sophisticated)
    // This is a simple check; a more robust way might involve a dedicated health-check endpoint
    // or a flag passed from the backend during initial page load.
    // For now, we rely on the error message from the chat API.
    // Example: if (localStorage.getItem('apiKeyMissing') === 'true') { apiKeyWarning.classList.remove('hidden'); }

    // --- PDF Processing Functionality ---
    if (processPdfButton) {
        processPdfButton.addEventListener('click', async () => {
            if (!pdfFileInput.files || pdfFileInput.files.length === 0) {
                pdfProcessStatus.textContent = 'Please select a PDF file first.';
                pdfProcessStatus.className = 'text-sm text-error mb-2';
                return;
            }
            const file = pdfFileInput.files[0];
            if (file.type !== 'application/pdf') {
                pdfProcessStatus.textContent = 'Invalid file type. Please select a PDF.';
                pdfProcessStatus.className = 'text-sm text-error mb-2';
                return;
            }

            storyCardsOutput.innerHTML = '<div class="text-center"><span class="loading loading-dots loading-lg text-secondary"></span><p>Analyzing PDF and generating story cards...</p></div>';
            pdfProcessStatus.textContent = 'Processing PDF... this may take a moment.';
            pdfProcessStatus.className = 'text-sm text-info mb-2';
            processPdfButton.disabled = true;
            processPdfButton.classList.add('btn-disabled');

            const formData = new FormData();
            formData.append('file', file);
            // Potentially add chunk_size here if you want to make it configurable from the UI
            // formData.append('chunk_size', 5); 

            try {
                const response = await fetch('/api/process-pdf', {
                    method: 'POST',
                    body: formData,
                });

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: 'Unknown error occurred during PDF processing.' }));
                    throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
                }

                const storyCards = await response.json();
                displayStoryCards(storyCards);
                pdfProcessStatus.textContent = `Successfully processed PDF. Found ${storyCards.length} story card(s).`;
                pdfProcessStatus.className = 'text-sm text-success mb-2';
                // pdfProcessorToggle.checked = true; // Keep the section open

            } catch (error) {
                console.error('Error processing PDF:', error);
                storyCardsOutput.innerHTML = `<p class="text-error">Error processing PDF: ${error.message}</p>`;
                pdfProcessStatus.textContent = `Error: ${error.message}`;
                pdfProcessStatus.className = 'text-sm text-error mb-2';
            } finally {
                processPdfButton.disabled = false;
                processPdfButton.classList.remove('btn-disabled');
            }
        });
    }

    function displayStoryCards(cards) {
        storyCardsOutput.innerHTML = ''; // Clear previous cards or loading indicator
        if (!cards || cards.length === 0) {
            storyCardsOutput.innerHTML = '<p class="text-neutral-content italic">No story cards were generated from this PDF, or the content was not suitable.</p>';
            return;
        }

        cards.forEach(card => {
            const cardDiv = document.createElement('div');
            cardDiv.className = 'card bg-base-100 shadow-xl mb-4 break-inside-avoid'; // Added break-inside-avoid for better layout if using columns

            const cardBody = document.createElement('div');
            cardBody.className = 'card-body p-4 md:p-6'; // Responsive padding

            let content = `<h3 class="card-title text-lg md:text-xl text-secondary-focus">${card.title || 'Untitled Card'}</h3>`;
            content += `<p class="text-xs text-accent mb-1"><strong>Framework:</strong> ${card.story_framework || 'N/A'}</p>`;
            if (card.summary) {
                content += `<div class="mt-2"><strong class="block text-sm text-neutral-focus">Summary:</strong><p class="text-sm text-base-content/80">${card.summary}</p></div>`;
            }
            if (card.key_elements && Object.keys(card.key_elements).length > 0) {
                content += `<div class="mt-2"><strong class="block text-sm text-neutral-focus">Key Elements:</strong><ul class="list-disc list-inside text-sm text-base-content/80">`;
                for (const [key, value] of Object.entries(card.key_elements)) {
                    content += `<li><strong>${key.replace(/_/g, ' ')}:</strong> ${value}</li>`;
                }
                content += `</ul></div>`;
            }
            if (card.target_audience) {
                 content += `<div class="mt-2"><strong class="block text-sm text-neutral-focus">Target Audience:</strong><p class="text-sm text-base-content/80">${card.target_audience}</p></div>`;
            }
            if (card.call_to_action) {
                content += `<div class="mt-2"><strong class="block text-sm text-neutral-focus">Call to Action:</strong><p class="text-sm text-base-content/80">${card.call_to_action}</p></div>`;
            }
            if (card.source_pages && card.source_pages.length > 0) {
                content += `<p class="text-xs text-neutral/70 mt-3"><em>Source Pages: ${card.source_pages.join(', ')}</em></p>`;
            }

            cardBody.innerHTML = content;
            cardDiv.appendChild(cardBody);
            storyCardsOutput.appendChild(cardDiv);
            
            // Animate card appearance
            anime({
                targets: cardDiv,
                opacity: [0, 1],
                translateY: [20, 0],
                delay: anime.stagger(100), // Stagger animation for multiple cards
                duration: 500,
                easing: 'easeOutExpo'
            });
        });
    }

    // --- Initializations ---
    fetchStoryTactics();
});
